import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/app_data.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Utils/bio_metric_authenticator.dart';
import 'package:simple_circular_progress_bar/simple_circular_progress_bar.dart';

class SafeAppList extends StatefulWidget {
  const SafeAppList({super.key});

  @override
  State<SafeAppList> createState() => _SafeAppListState();
}

class _SafeAppListState extends State<SafeAppList> {
  TextEditingController searchController = TextEditingController();
  late List<AppData> allApps = [];
  late List<AppData> filteredApps = [];
  bool isLoading = true;
  double progress = 0.0;
  late ValueNotifier<double> progressNotifier;

  // Map to track selected apps
  Map<String, bool> selectedApps = {};

  // List to store safe app package names
  List<String> safeAppList = [];

  @override
  void initState() {
    super.initState();
    progressNotifier = ValueNotifier<double>(0.0);
    searchController.addListener(() {
      filterApps();
    });
    loadApps();
    loadSafeAppList();
  }

  @override
  void dispose() {
    progressNotifier.dispose();
    searchController.dispose();
    super.dispose();
  }

  // Load the safe app list from the config
  void loadSafeAppList() async {
    final services = ConfigManager().services;
    if (services != null && services.safeAppList != null) {
      setState(() {
        safeAppList = services.safeAppList!;
      });
    }
  }

  void loadApps() async {
    // Reset progress to 0
    setState(() {
      progress = 0.0;
      progressNotifier.value = 0.0;
    });

    await AppData.load_all_apps();

    // Initialize the selected apps map
    Map<String, bool> initialSelection = {};
    for (var app in AppData.all_apps) {
      initialSelection[app.package_name] =
          safeAppList.contains(app.package_name);
    }

    setState(() {
      allApps = AppData.all_apps;
      selectedApps = initialSelection;
      filterApps();
      isLoading = false;
    });
  }

  void filterApps() {
    String query = searchController.text.toLowerCase();
    setState(() {
      filteredApps = allApps
          .where((app) => app.app_label.toLowerCase().contains(query))
          .toList();
    });
  }

  // Save selected apps to the safe app list
  Future<void> saveSelectedApps() async {
    // Show biometric authentication dialog
    bool isAuthenticated =
        await BiometricAuth().authenticateWithMasterFingerprint();

    if (!isAuthenticated) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Authentication failed. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Get the list of selected app package names
    List<String> selectedAppPackages = [];
    selectedApps.forEach((packageName, isSelected) {
      if (isSelected) {
        selectedAppPackages.add(packageName);
      }
    });

    // Update the safe app list in the config
    final services = ConfigManager().services;
    if (services != null) {
      await services.updateService('safeAppList', selectedAppPackages);

      // Refresh the safe app list
      loadSafeAppList();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Safe app list updated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Safe App List'),
        backgroundColor: Colors.green,
      ),
      body: isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SimpleCircularProgressBar(
                    mergeMode: true,
                    valueNotifier: progressNotifier,
                    progressColors: const [Colors.green],
                    backStrokeWidth: 10,
                    progressStrokeWidth: 10,
                    animationDuration: 1,
                    onGetText: (double value) {
                      return Text('${value.toInt()}%',
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold));
                    },
                  ),
                  const SizedBox(height: 16),
                  const Text('Loading apps...'),
                ],
              ),
            )
          : Column(
              children: [
                // Search bar
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: searchController,
                    decoration: InputDecoration(
                      labelText: 'Search Apps',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),

                // App list
                Expanded(
                  child: ListView.builder(
                    itemCount: filteredApps.length,
                    itemBuilder: (context, index) {
                      final app = filteredApps[index];
                      final bool isInSafeList =
                          safeAppList.contains(app.package_name);

                      return Card(
                        elevation: 2,
                        margin: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        color: isInSafeList ? Colors.green.shade50 : null,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: isInSafeList
                              ? BorderSide(
                                  color: Colors.green.shade300, width: 1)
                              : BorderSide.none,
                        ),
                        child: CheckboxListTile(
                          value: selectedApps[app.package_name] ?? false,
                          onChanged: (bool? value) {
                            setState(() {
                              selectedApps[app.package_name] = value ?? false;
                            });
                          },
                          title: Text(
                            app.app_label,
                            style: TextStyle(
                              fontWeight: isInSafeList
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                          subtitle: isInSafeList
                              ? const Text('In Safe List',
                                  style: TextStyle(color: Colors.green))
                              : null,
                          secondary: app.icon != null
                              ? Image.memory(
                                  app.icon!,
                                  width: 40,
                                  height: 40,
                                  fit: BoxFit.cover,
                                )
                              : const Icon(
                                  Icons.android,
                                  size: 40,
                                  color: Colors.grey,
                                ),
                          activeColor: Colors.green,
                          checkColor: Colors.white,
                          controlAffinity: ListTileControlAffinity.leading,
                        ),
                      );
                    },
                  ),
                ),

                // Add to safe list button
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 5,
                        offset: const Offset(0, -3),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: saveSelectedApps,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Add to Safe List',
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
