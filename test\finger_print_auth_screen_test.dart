import 'package:flutter/material.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_test/flutter_test.dart';

class FingerprintAuthScreen extends StatefulWidget {
  @override
  _FingerprintAuthScreenState createState() => _FingerprintAuthScreenState();
}

class _FingerprintAuthScreenState extends State<FingerprintAuthScreen> {
  final LocalAuthentication _auth = LocalAuthentication();
  bool _isAuthenticating = false;
  bool _showPinInput = false;

  TextEditingController _pinController = TextEditingController();

  Future<void> _authenticate() async {
    setState(() => _isAuthenticating = true);

    try {
      bool isAuthenticated = await _auth.authenticate(
        localizedReason: 'Scan your fingerprint to proceed',
        options: const AuthenticationOptions(biometricOnly: true),
      );

      if (isAuthenticated) {
        _navigateToProgressScreen();
      } else {
        setState(() => _showPinInput = true);
      }
    } catch (e) {
      print("Authentication error: $e");
      setState(() => _showPinInput = true);
    }

    setState(() => _isAuthenticating = false);
  }

  void _validatePin() {
    if (_pinController.text == "1234") {
      // Replace with a securely stored PIN
      _navigateToProgressScreen();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Invalid PIN. Try again.')),
      );
    }
  }

  void _navigateToProgressScreen() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
          builder: (context) => ProgressScreen()), // Replace with actual screen
    );
  }

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(milliseconds: 500), _authenticate);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: _showPinInput
            ? _buildPinInput()
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AnimatedOpacity(
                    opacity: _isAuthenticating ? 0.5 : 1.0,
                    duration: Duration(milliseconds: 500),
                    child: Icon(
                      Icons.fingerprint,
                      size: 100,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 20),
                  Text(
                    _isAuthenticating ? "Scanning..." : "Tap to authenticate",
                    style: TextStyle(color: Colors.white70, fontSize: 18),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildPinInput() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text("Enter PIN", style: TextStyle(color: Colors.white, fontSize: 20)),
        SizedBox(height: 10),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 40),
          child: TextField(
            controller: _pinController,
            obscureText: true,
            style: TextStyle(color: Colors.white),
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.grey[800],
              border:
                  OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
              hintText: '****',
              hintStyle: TextStyle(color: Colors.white54),
            ),
          ),
        ),
        SizedBox(height: 20),
        ElevatedButton(
          onPressed: _validatePin,
          child: Text("Confirm"),
        ),
      ],
    );
  }
}

class ProgressScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Text("Your progress is shown here"),
      ),
    );
  }
}

void main() {
  test('Fingerprint authentication should fail for unregistered users',
      () async {
    final auth =
        FingerprintAuthScreen(); // Replace with your auth service class
  });
}
