import 'package:flutter/material.dart';
import '../permissions/permission_status.dart';
import '../permissions/permission_types.dart';

/// A card that displays information about a permission and allows the user to request it.
class PermissionCard extends StatelessWidget {
  /// The permission to display.
  final PermissionStatus permission;

  /// Callback that is called when the user requests the permission.
  final VoidCallback onRequestPermission;

  /// Creates a new permission card.
  const PermissionCard({
    Key? key,
    required this.permission,
    required this.onRequestPermission,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Special handling for Device Admin permission
    final bool isDeviceAdmin = permission.type == PermissionType.deviceAdmin;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(permission.iconPath, height: 120),
            const SizedBox(height: 32),
            Text(
              permission.title,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              permission.description,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            if (isDeviceAdmin) ...[
              const SizedBox(height: 16),
              const Text(
                'After enabling Device Admin in settings, the app will automatically continue.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic),
              ),
            ],
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: onRequestPermission,
              child: Text(
                  isDeviceAdmin ? 'Grant Admin Access' : 'Grant Permission'),
            ),
          ],
        ),
      ),
    );
  }
}
