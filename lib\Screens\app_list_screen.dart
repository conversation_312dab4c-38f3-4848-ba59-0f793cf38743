import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/app_data.dart';
import 'package:simple_circular_progress_bar/simple_circular_progress_bar.dart';

class AppList extends StatefulWidget {
  const AppList({super.key});

  @override
  State<AppList> createState() => _AppListState();
}

class _AppListState extends State<AppList> {
  TextEditingController searchController = TextEditingController();
  late List<AppData> allApps = [];
  late List<AppData> filteredApps = [];
  bool isLoading = true;
  double progress = 0.0;
  late ValueNotifier<double> progressNotifier;

  @override
  void initState() {
    super.initState();
    progressNotifier = ValueNotifier<double>(0.0);
    searchController.addListener(() {
      filterApps();
    });
    loadApps();
  }

  @override
  void dispose() {
    progressNotifier.dispose();
    searchController.dispose();
    super.dispose();
  }

  void loadApps() async {
    // Reset progress to 0
    setState(() {
      progress = 0.0;
      progressNotifier.value = 0.0;
    });

    await AppData.load_all_apps(
      onProgress: (double value) {
        // Update both the progress variable and the notifier
        setState(() {
          progress = value;
        });
        // Make sure to update the UI by setting the value on the main thread
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Update the progress notifier with the percentage value (0-100)
          progressNotifier.value = value * 100;
          print('Progress updated: ${value * 100}%');
        });
      },
    );

    setState(() {
      allApps = AppData.all_apps;
      filterApps();
      isLoading = false;
    });
  }

  void filterApps() {
    String query = searchController.text.toLowerCase();
    setState(() {
      filteredApps = allApps
          .where((app) => app.app_label.toLowerCase().contains(query))
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Installed Apps'),
      ),
      body: isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SimpleCircularProgressBar(
                    mergeMode: true,
                    valueNotifier: progressNotifier,
                    progressColors: const [Colors.blue],
                    backStrokeWidth: 10,
                    progressStrokeWidth: 10,
                    animationDuration: 1,
                    onGetText: (double value) {
                      return Text('${value.toInt()}%',
                          style: const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold));
                    },
                  ),
                  const SizedBox(height: 16),
                  const Text('Loading apps...', style: TextStyle(fontSize: 16)),
                ],
              ),
            )
          : Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: searchController,
                    decoration: InputDecoration(
                      labelText: 'Search Apps',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: filteredApps.length,
                    itemBuilder: (context, index) {
                      return ListTile(
                        leading: filteredApps[index].icon != null
                            ? Image.memory(
                                filteredApps[index].icon!,
                                width: 30,
                                height: 30,
                                fit: BoxFit.cover,
                              )
                            : Icon(
                                Icons.android, // Fallback icon
                                size: 30,
                                color: Colors.grey,
                              ),
                        title: Text(filteredApps[index].app_label),
                        subtitle: filteredApps[index].is_blocked
                            ? Text("Blocked from " +
                                filteredApps[index]
                                    .blocking_start_time
                                    .toString() +
                                " till " +
                                filteredApps[index]
                                    .blocking_end_time
                                    .toString())
                            : null,
                        trailing: filteredApps[index].is_blocked
                            ? Icon(Icons.lock, color: Colors.red)
                            : Icon(Icons.lock_open, color: Colors.green),
                        onTap: () {
                          // On tap, show action items to perform -
                          // if the app is already blocked then show unblock end time
                          // otherwise add
                        },
                      );
                    },
                  ),
                )
              ],
            ),
    );
  }
}
