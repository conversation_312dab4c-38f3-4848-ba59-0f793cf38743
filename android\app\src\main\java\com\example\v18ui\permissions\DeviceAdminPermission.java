package com.example.v18ui.permissions;

import android.app.Activity;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.example.v18ui.receivers.DeviceAdminReceiver;

/**
 * Handles device admin permission operations.
 * This class provides methods to check if the device admin permission is granted
 * and to open the device admin settings.
 */
public class DeviceAdminPermission implements PermissionHandler {
    private static final String TAG = "DeviceAdminPermission";
    private final Context context;

    public DeviceAdminPermission(Context context) {
        this.context = context;
    }

    @Override
    public boolean isPermissionGranted() {
        try {
            DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
            ComponentName adminComponent = new ComponentName(context, "com.example.v18ui.receivers.DeviceAdminReceiver");
            Log.d(TAG, "Checking Device Admin status for component: " + adminComponent.getClassName());
            return dpm.isAdminActive(adminComponent);
        } catch (Exception e) {
            Log.e(TAG, "Failed to check Device Admin status", e);
            return false;
        }
    }

    @Override
    public void openSettings() {
        Log.d(TAG, "Opening Device Admin Settings");
        try {
            Intent intent = new Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN);
            ComponentName deviceAdmin = new ComponentName(context, "com.example.v18ui.receivers.DeviceAdminReceiver");
            Log.d(TAG, "Using DeviceAdmin component: " + deviceAdmin.getClassName());
            intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, deviceAdmin);
            intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION, "Enable device admin to secure the app and prevent uninstallation.");

            // Don't add FLAG_ACTIVITY_NEW_TASK to keep the settings activity in the same task
            // This will prevent the app from immediately returning to the permissions screen
            // intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

            // Start the activity with a result code to keep it in the foreground
            if (context instanceof Activity) {
                ((Activity) context).startActivityForResult(intent, 1);
                Log.d(TAG, "Device Admin Settings activity started with startActivityForResult");
            } else {
                // Fallback to regular startActivity if context is not an Activity
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                Log.d(TAG, "Device Admin Settings activity started with startActivity");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to open Device Admin Settings", e);
        }
    }
}
