import 'package:flutter/material.dart';
import 'package:v18ui/services/auth_service.dart';
import 'package:v18ui/Screens/home_screen.dart';
import 'package:v18ui/Screens/permission_screen.dart';
import 'package:v18ui/Screens/master_fingerprint_screen.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/config.dart';
import 'package:v18ui/Utils/device_utils.dart';
import 'package:v18ui/permissions/permission_manager.dart';
import 'package:v18ui/permissions/permission_types.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final AuthService _authService = AuthService();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkIfAlreadySignedIn();
  }

  Future<void> _checkIfAlreadySignedIn() async {
    setState(() {
      _isLoading = true;
    });

    final isSignedIn = await _authService.isSignedIn();

    if (isSignedIn && mounted) {
      _navigateToHome();
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _signInWithGoogle() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('Attempting to sign in with Google...');
      final user = await _authService.signInWithGoogle();

      if (user != null && mounted) {
        print('Sign-in successful');

        // Update the config with the user ID
        final configManager = ConfigManager();
        if (configManager.config != null) {
          // Get device ID
          final deviceId = await DeviceUtils.getDeviceId();

          final updatedConfig = Config(
            userId: deviceId,
            isFirstRun: configManager.config!.isFirstRun,
            subscription: configManager.config!.subscription,
            isMasterFingerprintActivated:
                configManager.config!.isMasterFingerprintActivated,
          );

          // Update the config
          configManager.config = updatedConfig;
          await configManager.saveConfig();
        }

        // Check if any permissions are missing
        final missingPermissions =
            await PermissionManager.getMissingPermissions();
        final bool hasAllPermissions = missingPermissions.isEmpty;

        // Direct check for usage stats permission
        final bool usageStatsGranted =
            await PermissionManager.isPermissionGranted(
                PermissionType.usageStats);
        print('Usage Stats permission granted: $usageStatsGranted');

        final config = ConfigManager().config;
        final bool hasMasterFingerprint =
            config != null && config.isMasterFingerprintActivated;

        print(
            'Sign-in screen - Missing permissions: ${missingPermissions.length}');

        if (mounted) {
          if (!hasAllPermissions || !usageStatsGranted) {
            // Navigate to the permissions screen if permissions are missing
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                builder: (context) => PermissionScreen(
                  onAllGranted: () {
                    // After permissions are granted, check if master fingerprint is set up
                    if (!hasMasterFingerprint) {
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                const MasterFingerprintScreen()),
                      );
                    } else {
                      // If master fingerprint is already set up, go to home screen
                      Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const HomeScreen()),
                      );
                    }
                  },
                ),
              ),
            );
          } else if (!hasMasterFingerprint) {
            // If all permissions are granted but no master fingerprint, go to master fingerprint screen
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => const MasterFingerprintScreen()),
            );
          } else {
            // If all conditions are met, go to home screen
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const HomeScreen()),
            );
          }
        }
      } else if (mounted) {
        print('Sign-in failed, user is null');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Sign-in failed. Please try again.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('Exception during sign-in: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToHome() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => const HomeScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/icons/v18_logo.png',
                    height: 150,
                  ),
                  const SizedBox(height: 40),
                  const Text(
                    'Welcome to V18',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 40),
                    child: Text(
                      'Your digital wellbeing companion to help you maintain a healthy relationship with technology',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                  const SizedBox(height: 60),
                  ElevatedButton.icon(
                    onPressed: _signInWithGoogle,
                    icon: const Icon(Icons.login),
                    label: const Text('Sign in with Google'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
