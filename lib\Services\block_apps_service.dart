import 'package:v18ui/DataObjects/blocked_apps.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Services/Service.dart';

class BlockAppsService {
  List<BlockedApp>? appsBlocked;
  bool? serviceActive;
  final bool userGenerated = false;

  BlockAppsService({this.appsBlocked, this.serviceActive});

  BlockAppsService? get blockAppsServiceData =>
      ConfigManager().services?.appBlockService;

  List<BlockedApp>? get appsBlockedData => blockAppsServiceData?.appsBlocked;

  set blockAppsServiceData(BlockAppsService? value) {
    ConfigManager().services!.appBlockService = value;
  }

  set appsBlockedData(List<BlockedApp>? value) {
    blockAppsServiceData!.appsBlocked = value;
  }

  factory BlockAppsService.fromJson(Map<String, dynamic> json) {
    return BlockAppsService(
      serviceActive: json['serviceActive'],
      appsBlocked: List<BlockedApp>.from(json['appsBlocked']
          ?.map((app) => BlockedApp.fromJson(app as Map<String, dynamic>))
          .toList()),
    );
  }

  Map<String, dynamic> toJson() => {
        'serviceActive': serviceActive,
        'appsBlocked': appsBlocked?.map((x) => x.toJson()).toList() ?? [],
      };

  Future<bool> updateService() async {
    return await Service()
        .updateService('appsBlocked', toJson()['appsBlocked']);
  }

  Future<bool> deleteService() async {
    return await Service().deleteService('appsBlocked');
  }
}
