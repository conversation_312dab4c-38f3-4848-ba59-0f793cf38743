import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

/// Utility class for device-related operations
class DeviceUtils {
  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  
  /// Get a unique device ID
  static Future<String> getDeviceId() async {
    try {
      if (Platform.isAndroid) {
        final AndroidDeviceInfo androidInfo = await _deviceInfoPlugin.androidInfo;
        // Use a combination of values to create a unique ID
        return '${androidInfo.id}_${androidInfo.model}_${androidInfo.brand}';
      } else if (Platform.isIOS) {
        final IosDeviceInfo iosInfo = await _deviceInfoPlugin.iosInfo;
        return iosInfo.identifierForVendor ?? 'unknown_ios_device';
      } else {
        // For other platforms, use a generic approach
        return 'unknown_device_${DateTime.now().millisecondsSinceEpoch}';
      }
    } catch (e) {
      print('Error getting device ID: $e');
      // Fallback to a timestamp-based ID if there's an error
      return 'fallback_device_${DateTime.now().millisecondsSinceEpoch}';
    }
  }
}
