import 'dart:convert';
import 'dart:io';
import 'package:v18ui/DataObjects/config.dart';
import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/Services/Service.dart';
import 'package:v18ui/Services/category_config.dart';
import 'package:v18ui/Services/category_settings.dart';

class ConfigManager {
  static final ConfigManager _instance = ConfigManager._internal();

  factory ConfigManager() => _instance;

  ConfigManager._internal();

  Config? _config;
  Service? _services;
  Map<String, CategorySettings>? _categorySettings;

  late File configFile;
  late File servicesFile;
  late File categorySettingsFile;

  Future<void> initLoad() async {
    await loadConfig();
    await loadServices();
    await loadCategorySettings();
  }

  // Load Config from Local Storage
  Future<void> loadConfig() async {
    configFile = File(FilePaths.config);
    if (await configFile.exists()) {
      print("🟢 Config file exists — Loading from local storage...");
      // Read config from local storage
      String jsonString = await configFile.readAsString();
      _config = Config.fromJson(json.decode(jsonString));
    } else {
      print("🟠 Config file not found — Creating default config...");
      _config = await Config.createDefaultConfig();
    }
  }

  // Load Services from Local Storage
  Future<void> loadServices() async {
    servicesFile = File(FilePaths.services);
    if (await servicesFile.exists()) {
      print("🟢 Services file exists — Loading from local storage...");
      // Read services from local storage
      String jsonString = await servicesFile.readAsString();
      _services = Service.fromJson(json.decode(jsonString));
    } else {
      print("🟠 Services file not found — Creating default services...");
      _services = await Service.createDefaultServices();
      await Service.saveServices(_services);
    }
  }

  Future<void> loadCategorySettings() async {
    categorySettingsFile = File(FilePaths.categorySettings);
    if ((await categorySettingsFile.exists()) == true) {
      print("🟢 Category file exists — Loading from local storage...");
      _categorySettings = await CategorySettings.loadCategoryConfigSettings(
          await categorySettingsFile.readAsString());
    } else {
      print("🟠 Category file not found — Creating first app_category...");
      await CategoryConfig().defaultCategorySettings();
      await loadCategorySettings();
    }
  }

  // Getter for Config Data
  Config? get config => _config;
  Service? get services => _services;
  Map<String, CategorySettings>? get categorySettings => _categorySettings;

  // Setter for Config Data
  set config(Config? newConfig) {
    _config = newConfig;
  }

  set services(Service? newServices) {
    _services = newServices;
  }

  set categorySettings(Map<String, CategorySettings>? newCategorySettings) {
    _categorySettings = newCategorySettings;
  }
}
