import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:v18ui/DataObjects/config.dart';
import 'package:v18ui/Services/Service.dart';
import 'package:v18ui/DataObjects/subscription.dart';
import 'package:v18ui/Services/block_apps_service.dart';
import 'package:v18ui/Services/category_config.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';
import 'package:v18ui/Services/quit_gambling_service.dart';
import 'package:v18ui/Services/safe_browsing.dart';
import 'package:v18ui/utils/device_utils.dart';

class ConfigManager {
  static final ConfigManager _instance = ConfigManager._internal();

  factory ConfigManager() => _instance;

  ConfigManager._internal();

  Config? _config;
  Service? _services;
  CategoryConfig? _categoryConfig;

  late File configFile;
  late File servicesFile;
  late File categorySettingsFile;
  late bool isFirstrun;

  Future<void> initLoad() async {
    await loadConfig();
    await loadServices();
    // We dont have dependency on usage stats to check for installed apps , we are using package manager ,
    //so even if it is first run , it does not make a difference.
    if (!isFirstrun) {
      await loadCategorySettings();
    }
  }

  // Load Config from Local Storage
  Future<void> loadConfig() async {
    final dir = await getApplicationDocumentsDirectory();
    configFile = File('${dir.path}/config.json');
    if (await configFile.exists()) {
      print("🟢 Config file exists — Loading from local storage...");
      // Read config from local storage
      String jsonString = await configFile.readAsString();
      _config = Config.fromJson(json.decode(jsonString));
      isFirstrun = _config!.isFirstRun;
    } else {
      print("🟠 Config file not found — Creating default config...");
      _config = await _createDefaultConfig();
      isFirstrun = _config!.isFirstRun;
    }
  }

  // Load Services from Local Storage
  Future<void> loadServices() async {
    final dir = await getApplicationDocumentsDirectory();
    servicesFile = File('${dir.path}/services.json');
    if (await servicesFile.exists()) {
      print("🟢 Services file exists — Loading from local storage...");
      // Read services from local storage
      //String jsonString = await servicesFile.readAsString();
      String jsonString = '''
{
    "appBlockService": {
        "appsBlocked": [
            {
                "appLabel": "Youtube",
                "blockingEndTime": "2025-03-11 00:00:000",
                "blockingStartTime": "2025-03-04 00:00:000",
                "packageName": "com.google.android.youtube"
            },
            {
                "appLabel": "Chrome",
                "blockingEndTime": "2025-03-11 00:00:000",
                "blockingStartTime": "2025-03-04 00:00:000",
                "packageName": "com.android.chrome"
            }
        ],
        "serviceActive": false
    },
    "pornDeaddictionService": {
        "CalendarData": {
            "2025-06": {
                "allowedCount": 4,
                "porn_watched_dates": [
                    "2025-06-15T16:30:00.000Z",
                    "2025-06-10T16:30:00.000Z",
                    "2025-06-23T16:30:00.000Z"
                ],
                "stage": 5,
                "watchedCount": 3
            },
            "2025-05": {
				"allowedCount": 4,
                "porn_watched_dates": [
          "2025-05-12T16:30:00.000Z",
					"2025-05-18T16:30:00.000Z",
					"2025-05-30T16:30:00.000Z",
					"2025-05-03T16:30:00.000Z"
                ],
                "stage": 4,
				"watchedCount": 4
            },
			"2025-04": {
				"allowedCount": 4,
                "porn_watched_dates": [
             		"2025-04-03T16:30:00.000Z"
                ],
                "stage": 5,
				"watchedCount": 1
            },
			"2025-08": {
				"allowedCount": 2,
                "porn_watched_dates": [
                    "2025-08-12T16:30:00.000Z",
					"2025-08-18T16:30:00.000Z"
                ],
                "stage": 3,
				"watchedCount": 2
            }
        },
        "serviceActive": false
    },
    "quitGamblingService": {
        "serviceActive": false
    },
    "safeAppList": [
    ],
    "safeBrowsing": {
        "blockingDomainVersion": 0,
        "lastDomainSyncDate": "2025-06-15T19:43:38.354922"
    }
}
''';
      _services = Service.fromJson(json.decode(jsonString));
    } else {
      print("🟠 Services file not found — Creating default services...");
      await _createDefaultServices();
    }
  }

  Future<void> loadCategorySettings() async {
    final dir = await getApplicationDocumentsDirectory();
    categorySettingsFile = File('${dir.path}/v18_app_category_settings.json');
    if (await categorySettingsFile.exists()) {
      print("🟢 Category file exists — Loading from local storage...");
      // Read services from local storage
      String jsonString = await categorySettingsFile.readAsString();
      _services = Service.fromJson(json.decode(jsonString));
    } else {
      print("🟠 Category file not found — Creating first app_category...");
      await CategoryConfig().defaultCategorySettings();
      await loadCategorySettings();
    }
  }

  // Create Default Services (on First Run) (empty service json)
  Future<void> _createDefaultServices() async {
    _services = Service(
      appBlockService: BlockAppsService(serviceActive: false),
      pornDeaddictionService: PornDeaddictionService(serviceActive: false),
      safeAppList: [],
      safeBrowsing: SafeBrowsingService(
          blockingDomainVersion: 0, lastDomainSyncDate: DateTime.now()),
      quitGamblingService: QuitGamblingService(serviceActive: false),
    );
    await saveServices();
  }

  // Save Modified Services Back to Local Storage
  Future<void> saveServices() async {
    if (_services != null) {
      String jsonString = json.encode(_services?.toJson());
      await servicesFile.writeAsString(jsonString);
      print("✅ Services file updated!");
    }
  }

  // ✅ Create Default Config (on First Run)
  Future<Config> _createDefaultConfig() async {
    // Get device ID
    String deviceId = await DeviceUtils.getDeviceId();

    _config = Config(
        userId: deviceId, // use device_id as user_id
        isFirstRun: true,
        subscription: Subscription(
          status: "inactive",
          expiryDate: DateTime.now().toIso8601String(),
        ),
        isMasterFingerprintActivated: false);
    await saveConfig();

    return _config!;
  }

  // ✅ Save Modified Config Back to Local Storage
  Future<void> saveConfig() async {
    if (_config != null) {
      String jsonString = json.encode(_config?.toJson());
      await configFile.writeAsString(jsonString);
      print("✅ Config file updated!");
    }
  }

  // Getter for Config Data
  Config? get config => _config;
  Service? get services => _services;
  CategoryConfig? get categoryConfig => _categoryConfig;

  // Setter for Config Data
  set config(Config? newConfig) {
    _config = newConfig;
  }
}
