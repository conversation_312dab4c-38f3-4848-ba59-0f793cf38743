import 'package:v18ui/Services/Service.dart';

class QuitGamblingService {
  bool serviceActive;
  final bool userGenerated = false;

  QuitGamblingService({required this.serviceActive});

  factory QuitGamblingService.fromJson(Map<String, dynamic> json) {
    return QuitGamblingService(serviceActive: json['serviceActive'] ?? false);
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceActive': serviceActive,
    };
  }

  Future<bool> updateService() async {
    return await Service().updateService('quitGamblingService', toJson());
  }
}
