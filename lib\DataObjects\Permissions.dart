import 'dart:async';
import 'package:flutter/services.dart';

class Permissions {
  static const _channel = MethodChannel('AppHandler/AppData');

  static Future<bool> isAccessibilityEnabled() async {
    final bool result =
        await _channel.invokeMethod('isAccessibilityServiceEnabled');
    print('Accessibility enabled check result: $result');
    return result;
  }

  static Future<bool> isUsageStatsEnabled() async {
    return await _channel.invokeMethod('isUsageStatsEnabled');
  }

  static Future<bool> isDeviceAdminEnabled() async {
    return await _channel.invokeMethod('isDeviceAdminEnabled');
  }

  static Future<void> openAccessibilitySettings() async {
    await _channel.invokeMethod('openAccessibilitySettings');
  }

  static Future<void> openUsageStatsSettings() async {
    await _channel.invokeMethod('openUsageStatsSettings');
  }

  static Future<void> openDeviceAdminSettings() async {
    await _channel.invokeMethod('openDeviceAdminSettings');
  }

  /// Opens settings for a specific permission
  /// We no longer wait for the permission to be granted here
  /// Instead, we check when the app resumes in the PermissionScreen
  static Future<void> listenUntilGranted({
    required Future<bool> Function() check,
    required Future<void> Function() openSettings,
  }) async {
    // Just open the settings and return immediately
    // The permission check will happen when the app resumes
    await openSettings();

    // We still do one immediate check in case the permission was already granted
    // but the UI hasn't updated yet
    await Future.delayed(const Duration(milliseconds: 500));
    bool granted = await check();
    if (!granted) {
      // If not granted, we'll let the app lifecycle handle it
      // The app will check for permission status when it resumes
    }
  }

  /// Used to check which permissions are not yet granted
  static Future<List<Map<String, dynamic>>> getMissingPermissions() async {
    List<Map<String, dynamic>> permissions = [];

    if (!(await isAccessibilityEnabled())) {
      permissions.add({
        'title': 'Enable Accessibility Service',
        'description': 'Required for monitoring app usage.',
        'action': () => listenUntilGranted(
              check: isAccessibilityEnabled,
              openSettings: openAccessibilitySettings,
            ),
        'iconPath': 'assets/icons/accessibility_sticker.png',
      });
    }

    if (!(await isUsageStatsEnabled())) {
      permissions.add({
        'title': 'Enable Usage Stats',
        'description': 'Helps us detect app usage time.',
        'action': () => listenUntilGranted(
              check: isUsageStatsEnabled,
              openSettings: openUsageStatsSettings,
            ),
        'iconPath': 'assets/icons/usage_stats_sticker.png',
      });
    }

    if (!(await isDeviceAdminEnabled())) {
      permissions.add({
        'title': 'Enable Device Admin',
        'description': 'Required for preventing uninstalls.',
        'action': () => listenUntilGranted(
              check: isDeviceAdminEnabled,
              openSettings: openDeviceAdminSettings,
            ),
        'iconPath': 'assets/icons/device_admin_sticker.png',
      });
    }

    return permissions;
  }

  static Future<bool> areAllGranted() async {
    return await isAccessibilityEnabled() &&
        await isUsageStatsEnabled() &&
        await isDeviceAdminEnabled();
  }
}
