import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:v18ui/Services/watch_time_service.dart';

class FocusedYouTubeApp extends StatefulWidget {
  const FocusedYouTubeApp({super.key});

  @override
  State<FocusedYouTubeApp> createState() => _FocusedYouTubeAppState();
}

class _FocusedYouTubeAppState extends State<FocusedYouTubeApp>
    with TickerProviderStateMixin {
  late final WebViewController _webViewController;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  late final WatchTimeService _watchTimeService;

  Map<String, dynamic>? lastWatchedVideo;

  bool _hasSearched = false;
  bool _isLoading = false;
  bool _showSearchView =
      true; // Controls whether to show search view or YouTube view

  @override
  void initState() {
    super.initState();
    _watchTimeService = WatchTimeService();

    // Ensure search view is shown initially
    _showSearchView = true;

    const creationParams = PlatformWebViewControllerCreationParams();
    _webViewController =
        WebViewController.fromPlatformCreationParams(creationParams)
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setNavigationDelegate(
            NavigationDelegate(
              onNavigationRequest: (request) {
                final url = request.url;

                if (url.contains("youtube.com/shorts") ||
                    url.contains("/shorts/")) {
                  // This is a shorts video, show toast and redirect to last watched video
                  _showShortsBlockedToast();
                  if (lastWatchedVideo != null) {
                    playVideo(lastWatchedVideo!, context);
                  }
                  return NavigationDecision.prevent;
                }

                if (url.contains("youtube.com/") && !url.contains("/shorts/")) {
                  // Update last watched video
                  lastWatchedVideo = {'url': url};

                  // Check if user clicked on home icon
                  if (url == "https://www.youtube.com/" ||
                      url == "https://m.youtube.com/" ||
                      url.contains("youtube.com/feed/home")) {
                    // User clicked on home, show search view and load blank page
                    setState(() {
                      _showSearchView = true;
                    });

                    // Load a blank page to prevent YouTube's homepage from showing
                    _webViewController.loadRequest(Uri.parse('about:blank'));
                    return NavigationDecision
                        .prevent; // Prevent navigation to home page
                  }
                }

                return NavigationDecision.navigate;
              },
              onUrlChange: (UrlChange change) {
                final url = change.url ?? '';

                if (url.contains("youtube.com/shorts") ||
                    url.contains("/shorts/")) {
                  // This is a shorts video, show toast and redirect to last watched video
                  _showShortsBlockedToast();
                  if (lastWatchedVideo != null) {
                    playVideo(lastWatchedVideo!, context);
                  }
                } else if (url.contains("youtube.com/") &&
                    !url.contains("/shorts/")) {
                  // Update last watched video
                  lastWatchedVideo = {'url': url};

                  // Check if user clicked on home icon
                  if (url == "https://www.youtube.com/" ||
                      url == "https://m.youtube.com/" ||
                      url.contains("youtube.com/feed/home")) {
                    // User clicked on home, show search view and load blank page
                    setState(() {
                      _showSearchView = true;
                    });

                    // Load a blank page to prevent YouTube's homepage from showing
                    _webViewController.loadRequest(Uri.parse('about:blank'));
                  } else {
                    // User is on a different page, hide search view
                    setState(() {
                      _showSearchView = false;
                    });
                  }
                }
              },
            ),
          );
//          ..loadRequest(Uri.parse('about:blank')); // Start with a blank page

    // Initialize WebView
  }

  /// Shows a toast message when Shorts are blocked
  void _showShortsBlockedToast() {
    Fluttertoast.showToast(
      msg: "Shorts are disabled. Redirecting to previous video.",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.black87,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  /// Plays a video by loading its URL
  void playVideo(Map<String, dynamic> video, BuildContext context) {
    final videoUrl = video['url'];
    if (videoUrl != null && videoUrl.isNotEmpty) {
      _webViewController.loadRequest(Uri.parse(videoUrl));
    }
  }

  /// Handles a tap on a video item
  void handleVideoTap(Map<String, dynamic> videoItem, BuildContext context) {
    // Check if this is a shorts video
    final videoUrl = videoItem['url'] as String?;
    if (videoUrl != null && videoUrl.contains('/shorts/')) {
      // This is a shorts video, show toast and redirect to last watched video
      _showShortsBlockedToast();
      if (lastWatchedVideo != null) {
        playVideo(lastWatchedVideo!, context);
      }
    } else {
      // This is a regular video, play it
      playVideo(videoItem, context);
      // Update last watched video
      lastWatchedVideo = videoItem;
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) return;

    if (!_hasSearched) {
      setState(() {
        _hasSearched = true;
        _showSearchView = false; // Hide search view when search is performed
      });
    } else {
      setState(() {
        _showSearchView = false; // Hide search view when search is performed
      });
    }

    setState(() => _isLoading = true);
    _searchFocusNode.unfocus();

    final encodedQuery = Uri.encodeComponent(query);
    await _webViewController.loadRequest(
      Uri.parse('https://www.youtube.com/results?search_query=$encodedQuery'),
    );

    await Future.delayed(const Duration(milliseconds: 1000));
    setState(() => _isLoading = false);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the current orientation
    final isPortrait =
        MediaQuery.of(context).orientation == Orientation.portrait;

    return Scaffold(
      // Ensure the keyboard doesn't push content off the screen
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: const Text('Focused YouTube'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              if (_hasSearched) {
                _webViewController.reload();
              }
            },
          ),
          // Add a search button to the app bar
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              setState(() {
                _showSearchView = true;
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search view - shown when _showSearchView is true or when app first loads
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            // Use a more efficient approach for height
            height: _showSearchView
                ? (_hasSearched
                    // Smaller height after search
                    ? (isPortrait
                        ? 140.0 // Fixed height in portrait
                        : 120.0) // Fixed smaller height in landscape
                    // Initial search view (taller)
                    : (isPortrait
                        ? 200.0 // Fixed height in portrait
                        : 150.0)) // Fixed smaller height in landscape
                : 0, // Hide completely when not showing search view
            // Simplified layout with fewer nested containers
            child: _showSearchView
                ? Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: isPortrait ? 12.0 : 6.0,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min, // Take only needed space
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Only show title if not searched yet and in portrait mode
                        if (!_hasSearched && isPortrait)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 12.0),
                            child: Text(
                              'Search YouTube',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                          ),
                        // Search field - more compact in landscape mode
                        TextField(
                          controller: _searchController,
                          focusNode: _searchFocusNode,
                          onSubmitted: _performSearch,
                          textInputAction: TextInputAction.search,
                          // Adjust content padding based on orientation
                          style: TextStyle(fontSize: isPortrait ? 16.0 : 14.0),
                          decoration: InputDecoration(
                            hintText: 'Enter search terms...',
                            prefixIcon: const Icon(Icons.search),
                            suffixIcon: IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () => _searchController.clear(),
                              // Make icon smaller in landscape
                              iconSize: isPortrait ? 24.0 : 20.0,
                            ),
                            // Adjust content padding based on orientation
                            contentPadding: EdgeInsets.symmetric(
                              vertical: isPortrait ? 16.0 : 12.0,
                              horizontal: 16.0,
                            ),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(30.0)),
                            filled: true,
                            fillColor: Colors.grey[200],
                          ),
                        ),
                        // Small gap before button
                        SizedBox(height: isPortrait ? 12.0 : 6.0),
                        // Search button - more compact in landscape mode
                        ElevatedButton(
                          onPressed: () =>
                              _performSearch(_searchController.text),
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.0,
                              vertical: isPortrait ? 12.0 : 8.0,
                            ),
                          ),
                          child: const Text('Search'),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
          // YouTube WebView - always shown but expands when search view is hidden
          Expanded(
            child: Stack(
              children: [
                WebViewWidget(controller: _webViewController),
                if (_isLoading)
                  const Center(child: CircularProgressIndicator()),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
