import 'package:v18ui/Services/Service.dart';

class SafeBrowsingService {
  late int? blockingDomainVersion;
  late DateTime? lastDomainSyncDate;

  SafeBrowsingService({
    this.blockingDomainVersion,
    this.lastDomainSyncDate,
  });

  factory SafeBrowsingService.fromJson(Map<String, dynamic> json) {
    return SafeBrowsingService(
      blockingDomainVersion: json['blockingDomainVersion'] ?? 0,
      lastDomainSyncDate: json['lastDomainSyncDate'] ?? DateTime.now(),
    );
  }

  Map<String, dynamic> to_json() {
    return {
      'blockingDomainVersion': blockingDomainVersion,
      'lastDomainSyncDate': lastDomainSyncDate,
    };
  }

  Future<bool> updateService() async {
    return await Service().updateService('safeBrowsingService', to_json());
  }

  Future<bool> deleteService() async {
    return await Service().deleteService('safeBrowsingService');
  }
}
