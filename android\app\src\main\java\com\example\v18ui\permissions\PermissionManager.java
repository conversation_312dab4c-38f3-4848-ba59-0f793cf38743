package com.example.v18ui.permissions;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

/**
 * Main permission manager class that handles all permission-related operations.
 * This class coordinates between different permission handlers and provides a unified
 * interface for the Flutter side.
 */
public class PermissionManager implements MethodChannel.MethodCallHandler {
    private static final String TAG = "PermissionManager";

    private final Context context;
    private final AccessibilityPermission accessibilityPermission;
    private final UsageStatsPermission usageStatsPermission;
    private final DeviceAdminPermission deviceAdminPermission;

    public PermissionManager(Context context) {
        this.context = context;
        this.accessibilityPermission = new AccessibilityPermission(context);
        this.usageStatsPermission = new UsageStatsPermission(context);
        this.deviceAdminPermission = new DeviceAdminPermission(context);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        Log.d(TAG, "Method called: " + call.method);

        switch (call.method) {
            // Check permission status methods
            case "checkAccessibilityPermission":
                result.success(accessibilityPermission.isPermissionGranted());
                break;
            case "checkUsageStatsPermission":
                result.success(usageStatsPermission.isPermissionGranted());
                break;
            case "checkDeviceAdminPermission":
                result.success(deviceAdminPermission.isPermissionGranted());
                break;
            case "checkAllPermissions":
                result.success(checkAllPermissions());
                break;

            // Open settings methods
            case "openAccessibilitySettings":
                accessibilityPermission.openSettings();
                result.success(null);
                break;
            case "openUsageStatsSettings":
                usageStatsPermission.openSettings();
                result.success(null);
                break;
            case "openDeviceAdminSettings":
                Log.d(TAG, "Received openDeviceAdminSettings method call");
                deviceAdminPermission.openSettings();
                Log.d(TAG, "Completed openDeviceAdminSettings method call");
                result.success(null);
                break;

            default:
                result.notImplemented();
                break;
        }
    }

    /**
     * Checks the status of all permissions and returns a map with the results.
     * @return Map containing permission names and their status (true if granted, false otherwise)
     */
    private Map<String, Boolean> checkAllPermissions() {
        Map<String, Boolean> permissionStatus = new HashMap<>();

        permissionStatus.put("accessibility", accessibilityPermission.isPermissionGranted());
        permissionStatus.put("usageStats", usageStatsPermission.isPermissionGranted());
        permissionStatus.put("deviceAdmin", deviceAdminPermission.isPermissionGranted());

        return permissionStatus;
    }

    /**
     * Registers this permission manager with a method channel.
     * @param flutterEngine The Flutter engine to register with
     */
    public static void registerWith(io.flutter.embedding.engine.FlutterEngine flutterEngine, Context context) {
        MethodChannel channel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), "com.example.v18ui/permissions");
        channel.setMethodCallHandler(new PermissionManager(context));
    }
}
