import 'package:path_provider/path_provider.dart';

class FilePaths {
  static late String config;
  static late String services;
  static late String categorySettings;
  static late String common_categorised_apps;

  static Future loadFilePaths() async {
    final dir = await getApplicationDocumentsDirectory();
    config = '${dir.path}/config.json';
    services = '${dir.path}/services.json';
    categorySettings = '${dir.path}/v18_app_category_settings.json';
    common_categorised_apps = 'assets/files/common_categorised_apps.json';
  }
}
