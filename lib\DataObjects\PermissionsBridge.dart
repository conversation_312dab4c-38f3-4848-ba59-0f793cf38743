import 'package:v18ui/permissions/permission_manager.dart';
import 'package:v18ui/permissions/permission_types.dart';
import 'package:v18ui/permissions/permission_status.dart';

/// Bridge class to help transition from the old Permissions class to the new PermissionManager.
/// This class provides the same interface as the old Permissions class but uses the new PermissionManager internally.
class Permissions {
  /// Checks if the accessibility permission is granted.
  static Future<bool> isAccessibilityEnabled() async {
    return await PermissionManager.isPermissionGranted(PermissionType.accessibility);
  }
  
  /// Checks if the usage stats permission is granted.
  static Future<bool> isUsageStatsEnabled() async {
    return await PermissionManager.isPermissionGranted(PermissionType.usageStats);
  }
  
  /// Checks if the device admin permission is granted.
  static Future<bool> isDeviceAdminEnabled() async {
    return await PermissionManager.isPermissionGranted(PermissionType.deviceAdmin);
  }
  
  /// Opens the accessibility settings.
  static Future<void> openAccessibilitySettings() async {
    await PermissionManager.openPermissionSettings(PermissionType.accessibility);
  }
  
  /// Opens the usage stats settings.
  static Future<void> openUsageStatsSettings() async {
    await PermissionManager.openPermissionSettings(PermissionType.usageStats);
  }
  
  /// Opens the device admin settings.
  static Future<void> openDeviceAdminSettings() async {
    await PermissionManager.openPermissionSettings(PermissionType.deviceAdmin);
  }
  
  /// Opens settings for a specific permission.
  static Future<void> listenUntilGranted({
    required Future<bool> Function() check,
    required Future<void> Function() openSettings,
  }) async {
    // Just open the settings and return immediately
    await openSettings();
  }
  
  /// Gets a list of permissions that are not granted.
  static Future<List<Map<String, dynamic>>> getMissingPermissions() async {
    List<Map<String, dynamic>> permissions = [];
    List<PermissionStatus> missingPermissions = await PermissionManager.getMissingPermissions();
    
    for (var permission in missingPermissions) {
      permissions.add({
        'title': permission.title,
        'description': permission.description,
        'action': () => listenUntilGranted(
          check: () => _getCheckFunction(permission.type),
          openSettings: () => _getOpenSettingsFunction(permission.type),
        ),
        'iconPath': permission.iconPath,
      });
    }
    
    return permissions;
  }
  
  /// Checks if all permissions are granted.
  static Future<bool> areAllGranted() async {
    return await PermissionManager.areAllPermissionsGranted();
  }
  
  /// Gets the check function for a specific permission type.
  static Future<bool> _getCheckFunction(PermissionType type) async {
    switch (type) {
      case PermissionType.accessibility:
        return await isAccessibilityEnabled();
      case PermissionType.usageStats:
        return await isUsageStatsEnabled();
      case PermissionType.deviceAdmin:
        return await isDeviceAdminEnabled();
    }
  }
  
  /// Gets the open settings function for a specific permission type.
  static Future<void> _getOpenSettingsFunction(PermissionType type) async {
    switch (type) {
      case PermissionType.accessibility:
        await openAccessibilitySettings();
        break;
      case PermissionType.usageStats:
        await openUsageStatsSettings();
        break;
      case PermissionType.deviceAdmin:
        await openDeviceAdminSettings();
        break;
    }
  }
}
