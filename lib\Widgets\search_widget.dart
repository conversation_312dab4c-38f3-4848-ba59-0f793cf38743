/* 
  Author : <PERSON><PERSON>urpose : This search widget will send the typed words to server to check whether the words exists in our db or not
  Limitation : we are not implementing auto-completion , because server is created with the intention to search words
  we need to save words in the app , if you want to get auto-completion word suggestion , then what will be the use of 
  web server for word search. 
  */

/*import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

class search_widget extends StatefulWidget {
  const search_widget({super.key});

  @override
  State<search_widget> createState() => _search_widgetState();
}

class _search_widgetState extends State<search_widget> {
  TextEditingController _searchController = TextEditingController();
  bool _search_indicator = false;
  bool _isDoodleVisible = true;
  final FocusNode _focusNode = FocusNode();
  bool _isFocused = false;
  bool show_response = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        // Doodle
        AnimatedPositioned(
          duration: Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          top: _isFocused
              ? -150 // Move doodle out of view when focused
              : MediaQuery.of(context).padding.top + 80,
          left: 20,
          right: 20,
          child: AnimatedOpacity(
            opacity: _isFocused ? 0 : 1,
            duration: Duration(milliseconds: 200),
            child: Image.asset(
              'assets/Images/search doodle.png', // Example doodle image URL
              height: 100,
              fit: BoxFit.contain,
            ),
          ),
        ),

        // Search Bar
        AnimatedPositioned(
          duration: Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          top: _isFocused
              ? MediaQuery.of(context).padding.top + 10
              : MediaQuery.of(context).size.height * 0.4,
          left: 20,
          right: 20,
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(8),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: "Search...",
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              textInputAction: TextInputAction.search,
              onSubmitted: (searchTerm) async {
                var response = await sendSearchRequest(searchTerm);
                setState() {
                  show_response = true;
                }
              },
            ),
          ),
        ),
        // below line of code is still not working work from here
        show_response ? Center(child: Text('Response')) : Container(),
      ],
    ));
  }

  Future<Map<String, List>> sendSearchRequest(String searchTerm) async {
    print('sending search request');

    setState(() {
      _search_indicator = true;
    });

    // Define the URL of your Python server
    var url = Uri.parse('http://192.168.1.5:5000/word_search');

    // Define the data to be sent in the request body (in JSON format)
    var data = {'searchTerm': searchTerm.split(' ')};

    // Send the POST request
    var response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: json.encode(data),
    );

    // Handle the response
    if (response.statusCode == 200) {
      var responseBody = json.decode(response.body);
      if (responseBody['not_found_words'].length != 0) {
        print('not found words : ${responseBody['not_found_words']}');
        return {'response': responseBody['not_found_words']};
      } else {
        print('response from server - ${response.body}');
        return {
          'response': ['From here web view will be presented']
        };
      }
      //print('Search request successful');
      //print('Response body: ${response.body}');
    } else {
      // try to create a proper widget for this instead of returning single string
      print('Server is unreachable at the moment');
      return {
        'response': ['Server is unreachable at the moment']
      };
    }
  }
}*/
