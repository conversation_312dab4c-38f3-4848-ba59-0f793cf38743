import 'dart:convert';
import 'package:http/http.dart' as http;

class GPTService {
  final String apiKey;

  GPTService({required this.apiKey});

  /// Categorizes uncategorized package names using ChatGPT.
  Future<Map<String, List<String>>> categorizePackages(
      List<String> packageNames) async {
    if (packageNames.isEmpty) return {};

    // Step 1: Format the list into a string prompt
    final String prompt = _buildPrompt(packageNames);

    // Step 2: Make API request
    final response = await http.post(
      Uri.parse("https://api.openai.com/v1/chat/completions"),
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer $apiKey",
      },
      body: jsonEncode({
        "model": "gpt-4.1-nano",
        "temperature": 0,
        "messages": [
          {
            "role": "system",
            "content": "Classify package names"
                "Reply format: 'package name - Category name'"
                "Only use categories Entertainment, Gaming , adult , gambling else normal."
          },
          {"role": "user", "content": prompt}
        ]
      }),
    );

    // Step 3: Handle response
    if (response.statusCode == 200) {
      final content =
          jsonDecode(response.body)['choices'][0]['message']['content'];
      return _parseResponse(content);
    } else {
      throw Exception("Failed to categorize: ${response.body}");
    }
  }

  String _buildPrompt(List<String> packages) {
    return packages.join('\n');
  }

  Map<String, List<String>> _parseResponse(String content) {
    final Map<String, List<String>> categoryMap = {};

    final lines = content.trim().split('\n');
    for (var line in lines) {
      final parts = line.split(' - ');
      if (parts.length != 2) continue;

      final package = parts[0].trim();
      final category = parts[1].trim();

      categoryMap.putIfAbsent(category, () => []).add(package);
    }

    return categoryMap;
  }
}
