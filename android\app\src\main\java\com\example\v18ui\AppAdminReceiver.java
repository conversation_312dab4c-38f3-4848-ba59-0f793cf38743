package com.example.v18ui;

import android.app.admin.DeviceAdminReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;

public class AppAdminReceiver extends DeviceAdminReceiver {
    private static final String TAG = "AppAdminReceiver";

    @Override
    public void onEnabled(Context context, Intent intent) {
        Log.d(TAG, "Device Admin enabled successfully");
        Toast.makeText(context, "Device Admin enabled", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onDisabled(Context context, Intent intent) {
        Log.d(TAG, "Device Admin disabled");
        Toast.makeText(context, "Device Admin disabled", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        super.onReceive(context, intent);
        Log.d(TAG, "Received intent: " + intent.getAction());
    }

    @Override
    public CharSequence onDisableRequested(Context context, Intent intent) {
        // This method is called when the user tries to disable the device admin
        Log.d(TAG, "Device Admin disable requested");
        return "Disabling device admin will reduce the security of this app. Are you sure you want to proceed?";
    }
}
