package com.example.v18ui;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppData {

    Context context;
    String app_label;
    String package_name;
    byte[] icon;

    public AppData(Context context)
    {
        this.context = context;
    }

    
    /* Following function only sends package_names */
    /*public static List<String> get_user_installed_apps(PackageManager pkg_mgr)
    {
        List<String> user_installed = new ArrayList<>();
        List<String> IncludedSystemApp = new ArrayList<>();
        IncludedSystemApp.add("com.android.chrome");
        IncludedSystemApp.add("com.google.android.youtube");
        List<ApplicationInfo> apps = pkg_mgr.getInstalledApplications(0);
        for (ApplicationInfo appInfo : apps) {
            boolean isSystemApp = ((appInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0);
            String packageName = appInfo.packageName;

            if ((!isSystemApp || IncludedSystemApp.contains(packageName)))
            {
                user_installed.add(appInfo.packageName);
                
            }
        }
        return user_installed;
    }*/
    public static List<Map<String, Object>> get_Installed_apps(Context context)
    {
        PackageManager pkg_mgr = context.getPackageManager();
        List<String> IncludedSystemApp = new ArrayList<>();

        IncludedSystemApp.add("com.android.chrome");
        IncludedSystemApp.add("com.google.android.youtube");
        List<ApplicationInfo> apps = pkg_mgr.getInstalledApplications(0);
        List<Map<String,Object>> app_info = new ArrayList<>();
        for (ApplicationInfo info : apps) {
            boolean isSystemApp = ((info.flags & ApplicationInfo.FLAG_SYSTEM) != 0);
            String packageName = info.packageName;
            if ((!isSystemApp || IncludedSystemApp.contains(packageName)))
            {
                Map<String,Object> app_data = new HashMap<>();
                app_data.put("package_name", packageName);
                app_data.put("app_label", info.loadLabel(pkg_mgr));
                app_data.put("icon", get_icon(pkg_mgr, packageName));
                app_info.add(app_data);
            }
        }
        return app_info;
    }

    public static byte[] get_icon(PackageManager pkg_mgr , String Pkg_nm) {
        Drawable appIcon = null;
        try {
            appIcon = pkg_mgr.getApplicationIcon(Pkg_nm);
        } catch (PackageManager.NameNotFoundException e) {
                return new byte[0];
        }
        Bitmap appIconBitmap;
        if (appIcon instanceof BitmapDrawable) {
            appIconBitmap = ((BitmapDrawable) appIcon).getBitmap();
        } else {
            int width = appIcon.getIntrinsicWidth();
            int height = appIcon.getIntrinsicHeight();
            appIconBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(appIconBitmap);
            appIcon.setBounds(0, 0, width, height);
            appIcon.draw(canvas);
        }
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        appIconBitmap.compress(Bitmap.CompressFormat.PNG, 100, stream);
        return stream.toByteArray();
    }

}
