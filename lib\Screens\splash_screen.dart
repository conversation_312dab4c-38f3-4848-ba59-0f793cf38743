import 'dart:async';

import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/Screens/home_screen.dart';
import 'package:v18ui/Screens/initial_setup_screen.dart';
import 'package:v18ui/Screens/intro_screen.dart';
import 'package:v18ui/Screens/master_fingerprint_screen.dart';
import 'package:v18ui/permissions/permission_manager.dart';
import 'package:v18ui/permissions/permission_types.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    loadApp();
  }

  Future<void> loadApp() async {
    await FilePaths.loadFilePaths();
    await ConfigManager().initLoad();
    //AuthService().init(),     --google signin code should be removed

    if (!mounted) return;

    // Determine which screen to navigate to based on app state
    final config = ConfigManager().config;

    // Check permissions directly using both methods to debug
    final permissionStatus = await PermissionManager.checkAllPermissions();
    print('Permission status: $permissionStatus');

    // Direct check for usage stats permission
    final bool usageStatsGranted =
        await PermissionManager.isPermissionGranted(PermissionType.usageStats);
    print('Usage Stats permission granted: $usageStatsGranted');

    final missingPermissions = await PermissionManager.getMissingPermissions();
    final bool hasAllPermissions = missingPermissions.isEmpty;
    final bool hasMasterFingerprint =
        config != null && config.isMasterFingerprintActivated;

    print('Missing permissions: ${missingPermissions.length}');
    for (var permission in missingPermissions) {
      print('Missing permission: ${permission.type}');
    }

    // Decide which screen to show
    Widget destinationScreen;

    if (config!.isFirstRun) {
      // First run sequence: Intro -> Initial Setup -> Permissions -> MasterFingerprint
      destinationScreen = IntroScreen();
    } else if (!hasAllPermissions || !usageStatsGranted) {
      // Missing permissions - go through initial setup
      destinationScreen = const InitialSetupScreen();
    } else if (!hasMasterFingerprint) {
      // Has all permissions but no master fingerprint
      destinationScreen = const MasterFingerprintScreen();
    } else {
      // All conditions met, go to home screen
      destinationScreen = const HomeScreen();
    }

    // Navigate to the determined screen
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => destinationScreen),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/v18_logo.png',
              width: 150,
              height: 150,
            ),
            const SizedBox(height: 30),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ],
        ),
      ),
    );
  }
}
