import 'dart:async';

import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Screens/intro_screen.dart';
import 'package:v18ui/Screens/sign_in_screen.dart';
import 'package:v18ui/services/auth_service.dart';
import 'package:v18ui/Screens/permission_screen.dart';
import 'package:v18ui/Screens/master_fingerprint_screen.dart';
import 'package:v18ui/Screens/home_screen.dart';
import 'package:v18ui/permissions/permission_manager.dart';
import 'package:v18ui/permissions/permission_types.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

/// A wrapper widget for the permission screen that handles navigation
/// after permissions are granted.
class _PermissionScreenWrapper extends StatelessWidget {
  final bool hasMasterFingerprint;

  const _PermissionScreenWrapper({required this.hasMasterFingerprint});

  @override
  Widget build(BuildContext context) {
    return PermissionScreen(
      onAllGranted: () {
        // After permissions are granted, check if master fingerprint is set up
        if (!hasMasterFingerprint) {
          // Use the current context from this wrapper widget
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (context) => const MasterFingerprintScreen()),
          );
        } else {
          // If master fingerprint is already set up, go to home screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      },
    );
  }
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    loadApp();
  }

  Future<void> loadApp() async {
    await Future.wait([
      ConfigManager().initLoad(),
      AuthService().init(),
    ]);

    if (!mounted) return;

    // Determine which screen to navigate to based on app state
    final config = ConfigManager().config;
    final isSignedIn = await AuthService().isSignedIn();

    // Check permissions directly using both methods to debug
    final permissionStatus = await PermissionManager.checkAllPermissions();
    print('Permission status: $permissionStatus');

    // Direct check for usage stats permission
    final bool usageStatsGranted =
        await PermissionManager.isPermissionGranted(PermissionType.usageStats);
    print('Usage Stats permission granted: $usageStatsGranted');

    final missingPermissions = await PermissionManager.getMissingPermissions();
    final bool hasAllPermissions = missingPermissions.isEmpty;
    final bool hasMasterFingerprint =
        config != null && config.isMasterFingerprintActivated;

    print('Missing permissions: ${missingPermissions.length}');
    for (var permission in missingPermissions) {
      print('Missing permission: ${permission.type}');
    }

    // Decide which screen to show
    Widget destinationScreen;

    if (config!.isFirstRun) {
      // First run sequence: SignIn -> Permissions -> MasterFingerprint
      destinationScreen = IntroScreen();
    } else if (!isSignedIn) {
      // User is not signed in
      destinationScreen = const SignInScreen();
    } else if (!hasAllPermissions || !usageStatsGranted) {
      // User is signed in but missing permissions
      // We explicitly check for usageStatsGranted to handle the case where getMissingPermissions might not be accurate
      print(
          'Showing permission screen because hasAllPermissions=$hasAllPermissions, usageStatsGranted=$usageStatsGranted');
      // For the permission screen, we'll use a different approach
      // We'll create a new widget that handles the navigation after permissions are granted
      destinationScreen = _PermissionScreenWrapper(
        hasMasterFingerprint: hasMasterFingerprint,
      );
    } else if (!hasMasterFingerprint) {
      // User is signed in, has all permissions, but no master fingerprint
      destinationScreen = const MasterFingerprintScreen();
    } else {
      // All conditions met, go to home screen
      destinationScreen = const HomeScreen();
    }

    // Navigate to the determined screen
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => destinationScreen),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/v18_logo.png',
              width: 150,
              height: 150,
            ),
            const SizedBox(height: 30),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ],
        ),
      ),
    );
  }
}
