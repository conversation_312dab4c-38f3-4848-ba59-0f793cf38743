import 'dart:async';

import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/Screens/initial_setup_screen.dart';
import 'package:v18ui/Screens/intro_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    loadApp();
  }

  Future<void> loadApp() async {
    await FilePaths.loadFilePaths();
    await ConfigManager().initLoad();
    //AuthService().init(),     --google signin code should be removed

    if (!mounted) return;

    // Wait for a minimum splash duration
    await Future.delayed(const Duration(seconds: 2));

    // Determine which screen to navigate to based on basic config check
    final config = ConfigManager().config;

    // Decide which screen to show
    Widget destinationScreen;

    if (config == null || config.isFirstRun) {
      // First run - go to intro screen
      destinationScreen = IntroScreen();
    } else {
      // Not first run - let InitialSetupScreen handle all the permission/fingerprint logic
      destinationScreen = const InitialSetupScreen();
    }

    // Navigate to the determined screen
    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => destinationScreen),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/v18_logo.png',
              width: 150,
              height: 150,
            ),
            const SizedBox(height: 30),
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ],
        ),
      ),
    );
  }
}
