package com.example.v18ui.permissions;

import android.app.AppOpsManager;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.util.Log;

/**
 * Handles usage stats permission operations.
 * This class provides methods to check if the usage stats permission is granted
 * and to open the usage stats settings.
 */
public class UsageStatsPermission implements PermissionHandler {
    private static final String TAG = "UsageStatsPermission";
    private final Context context;
    
    public UsageStatsPermission(Context context) {
        this.context = context;
    }
    
    @Override
    public boolean isPermissionGranted() {
        try {
            AppOpsManager appOps = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
            int mode = appOps.checkOpNoThrow(
                    "android:get_usage_stats",
                    android.os.Process.myUid(),
                    context.getPackageName()
            );
            return mode == AppOpsManager.MODE_ALLOWED;
        } catch (Exception e) {
            Log.e(TAG, "Failed to check Usage Stats Permission", e);
            return false;
        }
    }
    
    @Override
    public void openSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Failed to open Usage Stats Settings", e);
        }
    }
}
