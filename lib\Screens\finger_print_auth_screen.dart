import 'package:flutter/material.dart';
//import 'package:local_auth/local_auth.dart';
import 'package:v18ui/Utils/bio_metric_authenticator.dart';

class FingerprintAuthModal extends StatefulWidget {
  final VoidCallback onAuthenticated;

  const FingerprintAuthModal({Key? key, required this.onAuthenticated})
      : super(key: key);

  @override
  _FingerprintAuthModalState createState() => _FingerprintAuthModalState();
}

class _FingerprintAuthModalState extends State<FingerprintAuthModal> {
  //final LocalAuthentication _auth = LocalAuthentication();
  bool _isAuthenticating = false;
  bool _showPinInput = false;
  final TextEditingController _pinController = TextEditingController();

  @override
  void initState() {
    super.initState();
    BiometricAuth().authenticateWithMasterFingerprint();
  }

/** modify it later */
  void _validatePin() {
    if (_pinController.text == "1234") {
      // Replace with secure PIN handling
      widget.onAuthenticated();
      Navigator.pop(context);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Invalid PIN. Try again.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.5, // Modal takes up 50% of screen
      minChildSize: 0.4,
      maxChildSize: 0.6,
      builder: (_, controller) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SingleChildScrollView(
            controller: controller,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.white30,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                const SizedBox(height: 20),
                _showPinInput ? _buildPinInput() : _buildFingerprintUI(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFingerprintUI() {
    return Column(
      children: [
        AnimatedOpacity(
          opacity: _isAuthenticating ? 0.5 : 1.0,
          duration: const Duration(milliseconds: 500),
          child: const Icon(
            Icons.fingerprint,
            size: 100,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),
        Text(
          _isAuthenticating ? "Scanning..." : "Tap to authenticate",
          style: const TextStyle(color: Colors.white70, fontSize: 18),
        ),
      ],
    );
  }

  Widget _buildPinInput() {
    return Column(
      children: [
        const Text("Enter PIN",
            style: TextStyle(color: Colors.white, fontSize: 20)),
        const SizedBox(height: 10),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40),
          child: TextField(
            controller: _pinController,
            obscureText: true,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.grey[800],
              border:
                  OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
              hintText: '****',
              hintStyle: const TextStyle(color: Colors.white54),
            ),
          ),
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          onPressed: _validatePin,
          child: const Text("Confirm"),
        ),
      ],
    );
  }
}
