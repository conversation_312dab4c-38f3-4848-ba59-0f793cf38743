package com.example.v18ui.services;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.Intent;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;

/**
 * Accessibility service for monitoring app usage.
 * This service receives accessibility events from the system and can be used
 * to monitor app usage, detect screen changes, etc.
 */
public class AppAccessibilityService extends AccessibilityService {
    private static final String TAG = "AppAccessibilityService";
    
    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        // Process accessibility events
        if (event.getEventType() == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            CharSequence packageName = event.getPackageName();
            if (packageName != null) {
                Log.d(TAG, "Current app: " + packageName);
            }
        }
    }
    
    @Override
    public void onInterrupt() {
        Log.d(TAG, "Accessibility Service Interrupted");
    }
    
    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        Log.d(TAG, "Accessibility Service Connected");
        
        // Configure the service
        AccessibilityServiceInfo info = getServiceInfo();
        if (info == null) {
            info = new AccessibilityServiceInfo();
        }
        
        // Set the types of events we want to receive
        info.eventTypes = AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED | 
                          AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED |
                          AccessibilityEvent.TYPE_WINDOWS_CHANGED;
        
        info.feedbackType = AccessibilityServiceInfo.FEEDBACK_VISUAL;
        info.notificationTimeout = 100;
        info.flags = AccessibilityServiceInfo.FLAG_REPORT_VIEW_IDS |
                     AccessibilityServiceInfo.FLAG_RETRIEVE_INTERACTIVE_WINDOWS;
        
        setServiceInfo(info);
    }
    
    @Override
    public boolean onUnbind(Intent intent) {
        Log.d(TAG, "Accessibility Service Unbound");
        return super.onUnbind(intent);
    }
}
