import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Screens/porn_deaddiction_mode_screen.dart';
import 'package:v18ui/Services/Service.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';

class PornDeaddictionOnboarding extends StatefulWidget {
  const PornDeaddictionOnboarding({super.key});

  @override
  State<PornDeaddictionOnboarding> createState() =>
      _PornDeaddictionOnboardingState();
}

class _PornDeaddictionOnboardingState extends State<PornDeaddictionOnboarding> {
  final List<String> sentences = [
    "You are much stronger than you think , all you have to do is to believe in yourself.",
    "You are not alone in this war , I will be your shield",
    "Let's start with knowing the stage of your addiction"
  ];

  int currentSentenceIndex = 0;
  List<String> words = [];
  List<bool> wordVisible = [];
  bool sentenceVisible = true;

  // Controller and variables for questionnaire
  final TextEditingController _daysController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _startSentenceAnimation();
  }

  @override
  void dispose() {
    _daysController.dispose();
    super.dispose();
  }

  Future<void> _startSentenceAnimation() async {
    while (currentSentenceIndex < sentences.length) {
      words = sentences[currentSentenceIndex].split(' ');
      wordVisible = List.generate(words.length, (_) => false);
      sentenceVisible = true;
      setState(() {});

      // Show words one by one
      for (int i = 0; i < words.length; i++) {
        await Future.delayed(const Duration(milliseconds: 300));
        if (mounted) {
          wordVisible[i] = true;
          setState(() {});
        }
      }

      // Wait before fade out
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        sentenceVisible = false;
        setState(() {});
      }

      await Future.delayed(const Duration(milliseconds: 500));
      currentSentenceIndex++;
      if (mounted) {
        setState(() {}); // Ensure UI updates when moving to next sentence
      }
    }

    // Ensure final state update to show questionnaire
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 500),
          child: currentSentenceIndex < sentences.length
              ? sentenceVisible
                  ? Wrap(
                      key: ValueKey(currentSentenceIndex),
                      spacing: 6,
                      alignment: WrapAlignment.center,
                      children: List.generate(words.length, (index) {
                        return AnimatedOpacity(
                          duration: const Duration(milliseconds: 300),
                          opacity: wordVisible[index] ? 1.0 : 0.0,
                          child: Text(
                            words[index],
                            style: GoogleFonts.notoSerif(
                                color: Colors.blue.shade200,
                                fontSize: 20,
                                fontWeight: FontWeight.bold),
                          ),
                        );
                      }),
                    )
                  : const SizedBox.shrink()
              : _buildQuestionaire(),
        ),
      ),
    );
  }

  Widget _buildQuestionaire() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Question text
            Text(
              "How many days do you masturbate in a week ?",
              style: GoogleFonts.notoSerif(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: Colors.blue.shade700,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Text field for number input
            TextFormField(
              controller: _daysController,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(1),
              ],
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.blue.shade600,
                    width: 2,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Error message display
            if (_errorMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade300),
                ),
                child: Text(
                  _errorMessage!,
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

            const SizedBox(height: 24),

            // Submit button
            ElevatedButton(
              onPressed: _validateAndSubmit,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: const Text(
                "Submit",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _validateAndSubmit() {
    setState(() {
      _errorMessage = null;
    });

    final inputText = _daysController.text.trim();

    if (inputText.isEmpty) {
      setState(() {
        _errorMessage = "Please enter a number";
      });
      return;
    }

    final inputValue = int.tryParse(inputText);

    if (inputValue == null) {
      setState(() {
        _errorMessage = "Please enter a valid number";
      });
      return;
    }

    if (inputValue < 1 || inputValue > 7) {
      setState(() {
        _errorMessage = "Please enter a number between 0 and 7";
      });
      return;
    }

    // Valid input - save the value
    setState(() {
      _errorMessage = null;
      PornDeaddictionService().insertRecord(PornDeaddictionService()
          .calculateStage(int.parse(_daysController.text)));
      PornDeaddictionService().pornDeaddictionServiceData!.serviceActive = true;
      PornDeaddictionService().updateService();
      Service.saveServices(ConfigManager().services!);
      Navigator.pushReplacement(context,
          MaterialPageRoute(builder: (context) => PornDeaddictionModeScreen()));
    });

    // Clear the text field after successful submission
    _daysController.clear();
  }
}
