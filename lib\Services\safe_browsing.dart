import 'package:v18ui/Services/Service.dart';

class SafeBrowsingService {
  int? blockingDomainVersion;
  DateTime? lastDomainSyncDate;

  SafeBrowsingService({this.blockingDomainVersion, this.lastDomainSyncDate});

  factory SafeBrowsingService.fromJson(Map<String, dynamic> json) {
    return SafeBrowsingService(
      blockingDomainVersion: json['blockingDomainVersion'] ?? 0,
      lastDomainSyncDate: json['lastDomainSyncDate'] != null
          ? DateTime.parse(json['lastDomainSyncDate'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'blockingDomainVersion': blockingDomainVersion,
      'lastDomainSyncDate': lastDomainSyncDate!.toIso8601String(),
    };
  }

  Future<bool> updateService() async {
    return await Service().updateService('safeBrowsing', toJson());
  }

  Future<bool> deleteService() async {
    return await Service().deleteService('safeBrowsing');
  }
}
