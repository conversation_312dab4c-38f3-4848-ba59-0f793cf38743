class BlockedApp {
  final String packageName;
  final String appLabel;
  final String blockingStartTime;
  final String blockingEndTime;

  BlockedApp({
    required this.packageName,
    required this.appLabel,
    required this.blockingStartTime,
    required this.blockingEndTime,
  });

  factory BlockedApp.fromJson(Map<String, dynamic> json) {
    return BlockedApp(
      packageName: json['packageName'],
      appLabel: json['appLabel'],
      blockingStartTime: json['blockingStartTime'],
      blockingEndTime: json['blockingEndTime'],
    );
  }

  Map<String, dynamic> toJson() => {
        'packageName': packageName,
        'appLabel': appLabel,
        'blockingStartTime': blockingStartTime,
        'blockingEndTime': blockingEndTime,
      };
}
