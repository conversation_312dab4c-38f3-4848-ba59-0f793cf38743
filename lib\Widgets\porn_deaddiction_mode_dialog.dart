import 'package:flutter/material.dart';
import 'package:v18ui/Screens/porn_deaddiction_mode_screen.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';

class PornDeaddictionModeDialog extends StatelessWidget {
  const PornDeaddictionModeDialog({super.key});

  @override
  Widget build(BuildContext context) {
    /* Check if the user has already used our service before or not. If the user is a new user we need to 
      educate the user about the service and know the current stage of porn addiction. 
      If the user has already used our service before, we need to skip the questions. */

    /** User is a new user */
    if (PornDeaddictionService().calendarData!.isEmpty) {
      return AlertDialog(
        title: Text('Activate Porn Deaddiction Mode'),
        content: Text(
            '''This mode helps you to stop your porn addiction by blocking all adult sites. 
            Along with porn sites , it will also block few entertainment apps which contains explicit content ,
            which triggers your porn addiction.

            But before we proceed , we want to know your current stage of porn addiction. Click on proceed for next steps....

            Dont't worry , clicking on Proceed will not block your apps or sites....
          '''),
        actions: <Widget>[
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
            ),
            onPressed: () {
              Navigator.of(context).pop(true);
            },
            child: Text('Proceed'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(false);
            },
            child: Text('cancel'),
          )
        ],
      );
    }
    /** User is an active user of this service*/
    else if (PornDeaddictionService()
        .pornDeaddictionServiceData!
        .serviceActive!) {
      return AlertDialog(
        title: Text('Porn Deaddiction Mode'),
        content: Text(
            '''This service is activate right now. In oder to reduce distraction from your device , 
      few apps are blocked and all adult sites are blocked, You can use Chrome browser and Focused YouTube app for browsing.
      '''),
        actions: <Widget>[
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
            ),
            onPressed: () {
              Navigator.pop(context, true);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => PornDeaddictionModeScreen()));
            },
            child: Text('Open Brahmacharya page'),
          )
        ],
      );
    }
    /** User is having history of using this service but not active right now */
    else {
      return AlertDialog(
        title: Text('Porn Deaddiction Mode'),
        content: Text(
            '''This service is not activate right now. In oder to restart this service , click on activate button.
      '''),
        actions: <Widget>[
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
            ),
            onPressed: () {
              Navigator.pop(context, true);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => PornDeaddictionModeScreen()));
            },
            child: Text('Activate Porn Deaddiction Mode'),
          )
        ],
      );
    }
  }
}
