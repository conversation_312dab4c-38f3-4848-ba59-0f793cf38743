package com.example.v18ui;

import android.accessibilityservice.AccessibilityService;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;

public class AppAccessibilityService extends AccessibilityService {

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        // Example: Log the type of event
        Log.d("AccessibilityService", "Event: " + event.toString());
    }

    @Override
    public void onInterrupt() {
        Log.d("AccessibilityService", "Accessibility Service Interrupted");
    }

    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        Log.d("AccessibilityService", "Accessibility Service Connected");
    }
}
