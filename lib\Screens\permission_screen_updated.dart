import 'package:flutter/material.dart';
import '../permissions/permission_manager.dart';
import '../permissions/permission_status.dart';
import '../widgets/permission_card.dart';

/// A screen that guides the user through granting permissions.
class PermissionScreen extends StatefulWidget {
  /// The callback to call when all permissions are granted.
  final VoidCallback? onAllGranted;

  /// Creates a new [PermissionScreen].
  const PermissionScreen({Key? key, this.onAllGranted}) : super(key: key);

  @override
  State<PermissionScreen> createState() => _PermissionScreenState();
}

class _PermissionScreenState extends State<PermissionScreen>
    with WidgetsBindingObserver {
  /// The controller for the page view.
  late final PageController _pageController;

  /// The current page index.
  int _currentPageIndex = 0;

  /// Whether the screen is loading.
  bool _isLoading = true;

  /// The pages to display.
  List<Widget> _pages = [];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    WidgetsBinding.instance.addObserver(this);
    _initializePages();
  }

  @override
  void dispose() {
    _pageController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Check if the current permission has been granted
      _checkCurrentPermissionStatus();
    }
  }

  /// Initializes the pages to display.
  Future<void> _initializePages() async {
    // Get the missing permissions
    final List<PermissionStatus> missingPermissions =
        await PermissionManager.getMissingPermissions();

    // Create the pages
    final List<Widget> pages = [
      // Introduction page
      _buildIntroPage(),
    ];

    // Add a page for each missing permission
    for (final permission in missingPermissions) {
      pages.add(
        PermissionCard(
          permission: permission,
          onRequestPermission: () => _requestPermission(permission),
        ),
      );
    }

    // Add the completion page
    pages.add(_buildCompletionPage());

    // Update the state
    setState(() {
      _pages = pages;
      _isLoading = false;
    });
  }

  /// Checks if the current permission has been granted.
  /// If it has, moves to the next page.
  Future<void> _checkCurrentPermissionStatus() async {
    // Skip if we're on the intro or completion page
    if (_currentPageIndex == 0 ||
        _currentPageIndex == _pages.length - 1 ||
        _pages.isEmpty) {
      return;
    }

    // Get the current permission
    final List<PermissionStatus> missingPermissions =
        await PermissionManager.getMissingPermissions();

    // Check if all permissions are granted
    if (missingPermissions.isEmpty) {
      // All permissions granted, go to completion page
      _goToPage(_pages.length - 1);
      return;
    }

    // Check if the current permission is granted
    final int permissionIndex = _currentPageIndex - 1; // Adjust for intro page
    if (permissionIndex >= missingPermissions.length) {
      // Current permission is granted, go to next page
      _goToNextPage();
    }
  }

  /// Goes to the next page.
  void _goToNextPage() {
    if (_currentPageIndex < _pages.length - 1) {
      _goToPage(_currentPageIndex + 1);
    }
  }

  /// Goes to a specific page.
  void _goToPage(int pageIndex) {
    setState(() {
      _currentPageIndex = pageIndex;
    });

    _pageController.animateToPage(
      pageIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    // If we're on the completion page, call the onAllGranted callback
    if (pageIndex == _pages.length - 1 && widget.onAllGranted != null) {
      widget.onAllGranted!();
    }
  }

  /// Handles the request for a permission.
  /// Opens the settings screen for the permission.
  Future<void> _requestPermission(PermissionStatus permission) async {
    await PermissionManager.openPermissionSettings(permission.type);

    // Add a manual check for the permission status after a delay
    // This helps in case the app lifecycle events don't trigger properly
    Future.delayed(const Duration(seconds: 5), () async {
      if (mounted) {
        // Check if this specific permission is now granted
        final bool isGranted =
            await PermissionManager.isPermissionGranted(permission.type);
        print('Permission ${permission.type} granted: $isGranted');

        if (isGranted) {
          // If granted, move to the next page directly
          _goToNextPage();
        } else {
          // Otherwise, check all permissions to update the UI
          _checkCurrentPermissionStatus();
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(), // Disable swiping
        onPageChanged: (index) {
          setState(() {
            _currentPageIndex = index;
          });
        },
        children: _pages,
      ),
    );
  }

  /// Builds the introduction page.
  Widget _buildIntroPage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Welcome to Self',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'We need a few permissions to help you stay focused.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _goToNextPage,
              child: const Text('Get Started'),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the completion page.
  Widget _buildCompletionPage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'All Set!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'You\'ve granted all the necessary permissions. You\'re ready to go!',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                if (widget.onAllGranted != null) {
                  widget.onAllGranted!();
                }
              },
              child: const Text('Continue'),
            ),
          ],
        ),
      ),
    );
  }
}
