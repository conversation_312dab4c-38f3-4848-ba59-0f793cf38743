import 'dart:convert';

class CategorySettings {
  /**last_track_date attribute of app_category json is not initiated here because that is not required anywhere in UI , it 
 * is used on in foreground service.
 */
  final bool weeklyOffAllowed;
  final String dailyAllowedTime;
  final List<String> package;
  bool timeoutOver;
  String todayTotalWatchTime;
  String categoryName;
  String categoryType;

  CategorySettings({
    required this.weeklyOffAllowed,
    required this.dailyAllowedTime,
    required this.package,
    required this.timeoutOver,
    required this.todayTotalWatchTime,
    required this.categoryName,
    required this.categoryType,
  });

  factory CategorySettings.fromJson(Map<String, dynamic> json) {
    return CategorySettings(
      weeklyOffAllowed: json['weekly_off_allowed'],
      dailyAllowedTime: json['daily_allowed_time'],
      package: json['package'].cast<String>(),
      timeoutOver: json['timeout_over'],
      todayTotalWatchTime: json['todayTotalWatchTime'],
      categoryName: json['categoryName'],
      categoryType: json['categoryType'],
    );
  }

  Map<String, dynamic> toJson() => {
        'weekly_off_allowed': weeklyOffAllowed,
        'daily_allowed_time': dailyAllowedTime,
        'package': package,
        'timeout_over': timeoutOver,
        'todayTotalWatchTime': todayTotalWatchTime,
        'categoryName': categoryName,
        'categoryType': categoryType,
      };

/** We dont need shared preference , we will store our file in internal storage , this way we can read and modify files
 * from foreground service itself.
 */
  static Future<Map<String, CategorySettings>> loadCategoryConfigSettings(
      String jsonString) async {
    var settings_json = jsonDecode(jsonString);
    Map<String, CategorySettings> category_settings = {};
    for (final entry in settings_json.entries) {
      final categoryName = entry.key;
      final categoryData = entry.value;
      category_settings[categoryName] = CategorySettings.fromJson(categoryData);
    }
    return category_settings;
  }

  Future<Map<String, dynamic>> UpdateCategorySettings(
      String key, CategorySettings config) async {
    if (key == 'System_generated') {
      return {
        "result": false,
        "reason": "System generated category cannot be Update"
      };
    } else
      return {"result": true, "reason": "Following is not implemented yet"};
  }

  Future<Map<String, dynamic>> deleteUserCategorysettings(String key) async {
    if (key == 'System_generated') {
      return {
        "result": false,
        "reason": "System generated category cannot be deleted"
      };
    } else {
      return {"result": true, "reason": "Not implemented yet"};
    }
  }

  Future<Map<String, dynamic>> createUserCategorySettings(
      String key, CategorySettings config) async {
    return {"result": true, "reason": "Not implemented yet"};
  }
}
