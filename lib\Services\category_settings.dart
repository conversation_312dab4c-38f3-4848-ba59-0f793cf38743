import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

class CategorySettings {
  /**last_track_date attribute of app_category json is not initiated here because that is not required anywhere in UI , it 
 * is used on in foreground service.
 */
  final bool weeklyOffAllowed;
  final String dailyAllowedTime;
  final List<String> package;
  bool timeoutOver;
  String todayTotalWatchTime;
  String categoryName;
  String categoryType;

  CategorySettings({
    required this.weeklyOffAllowed,
    required this.dailyAllowedTime,
    required this.package,
    required this.timeoutOver,
    required this.todayTotalWatchTime,
    required this.categoryName,
    required this.categoryType,
  });

  factory CategorySettings.fromJson(Map<String, dynamic> json) {
    return CategorySettings(
      weeklyOffAllowed: json['weekly_off_allowed'],
      dailyAllowedTime: json['daily_allowed_time'],
      package: json['package'].cast<String>(),
      timeoutOver: json['timeout_over'],
      todayTotalWatchTime: json['todayTotalWatchTime'],
      categoryName: json['categoryName'],
      categoryType: json['categoryType'],
    );
  }

  Map<String, dynamic> toJson() => {
        'weekly_off_allowed': weeklyOffAllowed,
        'daily_allowed_time': dailyAllowedTime,
        'package': package,
        'timeout_over': timeoutOver,
        'todayTotalWatchTime': todayTotalWatchTime,
        'categoryName': categoryName,
        'categoryType': categoryType,
      };

  Future<Map<String, CategorySettings>> loadCategoryConfigSettings() async {
    final prefs = await SharedPreferences.getInstance();
    String settings = prefs.getString('v18_app_category_settings')!;
    var settings_json = jsonDecode(settings);
    Map<String, CategorySettings> category_settings = {};
    for (final entry in settings_json.entries) {
      final categoryName = entry.key;
      final categoryData = entry.value;
      category_settings[categoryName] = CategorySettings.fromJson(categoryData);
    }
    return category_settings;
  }

  Future<Map<String, dynamic>> UpdateCategorySettings(
      String key, CategorySettings config) async {
    String encodedData;
    final prefs = await SharedPreferences.getInstance();
    var existing_config =
        jsonDecode(prefs.getString("v18_app_category_settings")!);
    if (key == 'System_generated') {
      return {
        "result": false,
        "reason": "System generated category cannot be Update"
      };
    }
    if (existing_config.containsKey(key)) {
      existing_config[key] = config.toJson();
      encodedData = existing_config.toString();
      await prefs.setString('v18_app_category_settings', encodedData);
      return {"result": true, "reason": "Category updated successfully"};
    } else {
      return {"result": false, "reason": "Category does not exist"};
    }
  }

  Future<Map<String, dynamic>> deleteUserCategorysettings(String key) async {
    final prefs = await SharedPreferences.getInstance();
    var existing_config =
        jsonDecode(prefs.getString("v18_app_category_settings")!);
    if (key == 'System_generated') {
      return {
        "result": false,
        "reason": "System generated category cannot be deleted"
      };
    } else {
      existing_config.remove(key);
      String encodedData = existing_config.toString();
      await prefs.setString('v18_app_category_settings', encodedData);
      return {"result": true, "reason": "Category deleted successfully"};
    }
  }

  Future<Map<String, dynamic>> createUserCategorySettings(
      String key, CategorySettings config) async {
    final prefs = await SharedPreferences.getInstance();
    var existing_config =
        jsonDecode(prefs.getString("v18_app_category_settings")!);
    existing_config.addAll({key: config.toJson().toString()});
    String encodedData = existing_config.toString();
    await prefs.setString('v18_app_category_settings', encodedData);
    return {"result": true, "reason": "Category created successfully"};
  }
}
