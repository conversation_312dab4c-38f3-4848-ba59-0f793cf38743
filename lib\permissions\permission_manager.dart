import 'package:flutter/services.dart';
import 'permission_types.dart';
import 'permission_status.dart';

/// Main class for managing permissions in the app.
/// This class provides methods to check and request permissions.
class PermissionManager {
  static const MethodChannel _channel =
      MethodChannel('com.example.v18ui/permissions');

  /// Checks if a specific permission is granted.
  /// Returns true if the permission is granted, false otherwise.
  static Future<bool> isPermissionGranted(PermissionType permission) async {
    try {
      final String methodName = _getCheckMethodName(permission);
      final bool result = await _channel.invokeMethod(methodName);
      return result;
    } on PlatformException catch (e) {
      print('Error checking permission: ${e.message}');
      return false;
    }
  }

  /// Opens the settings screen for a specific permission.
  /// This allows the user to grant the permission.
  static Future<void> openPermissionSettings(PermissionType permission) async {
    try {
      final String methodName = _getOpenSettingsMethodName(permission);
      await _channel.invokeMethod(methodName);
    } on PlatformException catch (e) {
      print('Error opening permission settings: ${e.message}');
    }
  }

  /// Checks the status of all permissions.
  /// Returns a map with the status of each permission.
  static Future<Map<PermissionType, bool>> checkAllPermissions() async {
    try {
      final Map<dynamic, dynamic> result =
          await _channel.invokeMethod('checkAllPermissions');

      return {
        PermissionType.accessibility: result['accessibility'] ?? false,
        PermissionType.usageStats: result['usageStats'] ?? false,
        PermissionType.deviceAdmin: result['deviceAdmin'] ?? false,
      };
    } on PlatformException catch (e) {
      print('Error checking all permissions: ${e.message}');
      return {
        PermissionType.accessibility: false,
        PermissionType.usageStats: false,
        PermissionType.deviceAdmin: false,
      };
    }
  }

  /// Gets the method name for checking a specific permission.
  static String _getCheckMethodName(PermissionType permission) {
    switch (permission) {
      case PermissionType.accessibility:
        return 'checkAccessibilityPermission';
      case PermissionType.usageStats:
        return 'checkUsageStatsPermission';
      case PermissionType.deviceAdmin:
        return 'checkDeviceAdminPermission';
    }
  }

  /// Gets the method name for opening the settings screen for a specific permission.
  static String _getOpenSettingsMethodName(PermissionType permission) {
    switch (permission) {
      case PermissionType.accessibility:
        return 'openAccessibilitySettings';
      case PermissionType.usageStats:
        return 'openUsageStatsSettings';
      case PermissionType.deviceAdmin:
        return 'openDeviceAdminSettings';
    }
  }

  /// Gets a list of permissions that are not granted.
  /// Returns a list of PermissionStatus objects.
  static Future<List<PermissionStatus>> getMissingPermissions() async {
    final Map<PermissionType, bool> permissionStatus =
        await checkAllPermissions();
    final List<PermissionStatus> missingPermissions = [];

    print('Permission status: $permissionStatus');

    // Use null-aware operators to handle potential null values
    if (permissionStatus[PermissionType.accessibility] != true) {
      missingPermissions.add(
        PermissionStatus(
          type: PermissionType.accessibility,
          title: 'Enable Accessibility Service',
          description: 'Required for monitoring app usage.',
          iconPath: 'assets/icons/accessibility_sticker.png',
        ),
      );
    }

    if (permissionStatus[PermissionType.usageStats] != true) {
      missingPermissions.add(
        PermissionStatus(
          type: PermissionType.usageStats,
          title: 'Enable Usage Stats',
          description: 'Helps us detect app usage time.',
          iconPath: 'assets/icons/usage_stats_sticker.png',
        ),
      );
    }

    if (permissionStatus[PermissionType.deviceAdmin] != true) {
      missingPermissions.add(
        PermissionStatus(
          type: PermissionType.deviceAdmin,
          title: 'Enable Device Admin',
          description: 'Required for preventing uninstalls.',
          iconPath: 'assets/icons/device_admin_sticker.png',
        ),
      );
    }

    print('Missing permissions: ${missingPermissions.length}');
    return missingPermissions;
  }

  /// Checks if all permissions are granted.
  /// Returns true if all permissions are granted, false otherwise.
  static Future<bool> areAllPermissionsGranted() async {
    final Map<PermissionType, bool> permissionStatus =
        await checkAllPermissions();
    return permissionStatus.values.every((granted) => granted);
  }
}
