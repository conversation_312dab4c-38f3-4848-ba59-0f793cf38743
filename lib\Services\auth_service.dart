import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A simplified authentication service that uses Google Sign-In without Firebase
class AuthService {
  static final AuthService _instance = AuthService._internal();

  factory AuthService() => _instance;

  AuthService._internal();

  final GoogleSignIn _googleSignIn = GoogleSignIn();
  GoogleSignInAccount? _currentUser;

  /// Get the current signed-in user
  GoogleSignInAccount? get currentUser => _currentUser;

  /// Initialize the auth service
  Future<void> init() async {
    // Try to sign in silently if the user was previously signed in
    _currentUser = await _googleSignIn.signInSilently();
  }

  /// Sign in with Google
  Future<GoogleSignInAccount?> signInWithGoogle() async {
    try {
      print('Starting Google Sign-In flow...');
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        print('User canceled the sign-in flow');
        return null;
      }

      print('Google Sign-In successful: ${googleUser.email}');
      _currentUser = googleUser;

      // Save user info to shared preferences
      await _saveUserInfo(googleUser);

      return googleUser;
    } catch (error) {
      print('Google Sign-In Error: $error');
      return null;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    print('Signing out...');
    await _googleSignIn.signOut();
    _currentUser = null;

    // Clear user info from shared preferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_email');
    await prefs.remove('user_display_name');
    await prefs.remove('user_photo_url');
    print('Sign-out complete');
  }

  /// Check if the user is signed in
  Future<bool> isSignedIn() async {
    if (_currentUser != null) return true;

    // Try to sign in silently
    _currentUser = await _googleSignIn.signInSilently();
    return _currentUser != null;
  }

  /// Save user info to shared preferences
  Future<void> _saveUserInfo(GoogleSignInAccount user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_email', user.email);
    await prefs.setString('user_display_name', user.displayName ?? '');
    await prefs.setString('user_photo_url', user.photoUrl ?? '');
  }

  /// Get user info from shared preferences
  Future<Map<String, String>> getUserInfo() async {
    if (_currentUser != null) {
      return {
        'email': _currentUser!.email,
        'displayName': _currentUser!.displayName ?? '',
        'photoUrl': _currentUser!.photoUrl ?? '',
      };
    }

    final prefs = await SharedPreferences.getInstance();
    return {
      'email': prefs.getString('user_email') ?? '',
      'displayName': prefs.getString('user_display_name') ?? '',
      'photoUrl': prefs.getString('user_photo_url') ?? '',
    };
  }
}
