import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';
import 'package:encrypt/encrypt.dart';

class BiometricAuth {
  static final BiometricAuth _instance = BiometricAuth._internal();
  final LocalAuthentication _auth = LocalAuthentication();
  final FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  BiometricAuth._internal();

  factory BiometricAuth() {
    return _instance;
  }

// Generate a fingerprint-protected AES key and store it
  Future<void> generateMasterFingerprintKey() async {
    bool isAuthenticated = await _auth.authenticate(
      localizedReason: 'Register your master fingerprint',
      options: AuthenticationOptions(biometricOnly: true),
    );

    if (!isAuthenticated) {
      throw Exception("Authentication failed");
    }

    // Generate a 256-bit AES key
    final key = AES(Key.fromSecureRandom(32));

    // Encrypt a "Master Fingerprint Identifier"
    final encrypter = Encrypter(AES(key.key));
    final encryptedIdentifier =
        encrypter.encrypt("master_fingerprint", iv: IV.fromLength(16));

    // Store AES key **securely, locked to fingerprint**
    await _secureStorage.write(
      key: 'fingerprint_key',
      value: base64Encode(key.key.bytes),
      iOptions: IOSOptions(
          accessibility: KeychainAccessibility.first_unlock), // iOS only
      aOptions: AndroidOptions(
          encryptedSharedPreferences: true), // 🔥 Android Keystore
    );

    // Store encrypted identifier
    await _secureStorage.write(
      key: 'fingerprint_id',
      value: encryptedIdentifier.base64,
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
    );

    print("Master fingerprint registered successfully.");
  }

  Future<bool> authenticateWithMasterFingerprint() async {
    bool isAuthenticated = await _auth.authenticate(
      localizedReason: 'Authenticate to view progress',
      options: AuthenticationOptions(biometricOnly: true),
    );

    if (!isAuthenticated) {
      return false;
    }

    // 🔥 Try to retrieve the AES key (only master fingerprint can access it!)
    String? encryptedKey = await _secureStorage.read(
      key: 'fingerprint_key',
      aOptions: AndroidOptions(encryptedSharedPreferences: true),
    );

    if (encryptedKey == null) {
      print("Failed to retrieve fingerprint key!");
      return false;
    }

    // Retrieve encrypted identifier
    String? encryptedId = await _secureStorage.read(key: 'fingerprint_id');

    if (encryptedId == null) {
      print("Fingerprint identifier not found!");
      return false;
    }

    // Decrypt and verify identifier
    final key = AES(Key(base64Decode(encryptedKey)));
    final encrypter = Encrypter(AES(key.key));
    final decryptedId = encrypter.decrypt64(encryptedId, iv: IV.fromLength(16));

    if (decryptedId == "master_fingerprint") {
      print("Authenticated as master user!");
      return true;
    } else {
      print("Fingerprint does not match master user!");
      return false;
    }
  }
}
