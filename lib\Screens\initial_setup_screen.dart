import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/Screens/home_screen.dart';
import 'package:v18ui/Screens/permission_screen.dart';
import 'package:v18ui/Screens/master_fingerprint_screen.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/config.dart';
import 'package:v18ui/Utils/device_utils.dart';
import 'package:v18ui/permissions/permission_manager.dart';
import 'package:v18ui/permissions/permission_types.dart';

class InitialSetupScreen extends StatefulWidget {
  const InitialSetupScreen({super.key});

  @override
  State<InitialSetupScreen> createState() => _InitialSetupScreenState();
}

class _InitialSetupScreenState extends State<InitialSetupScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _performInitialSetup();
  }

  Future<void> _performInitialSetup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Check if any permissions are missing
      final missingPermissions =
          await PermissionManager.getMissingPermissions();
      final bool hasAllPermissions = missingPermissions.isEmpty;

      // Direct check for usage stats permission
      final bool usageStatsGranted =
          await PermissionManager.isPermissionGranted(
              PermissionType.usageStats);
      print('Usage Stats permission granted: $usageStatsGranted');

      final config = ConfigManager().config;
      final bool hasMasterFingerprint =
          config != null && config.isMasterFingerprintActivated;

      print(
          'Initial setup - Missing permissions: ${missingPermissions.length}');

      if (mounted) {
        if (!hasAllPermissions || !usageStatsGranted) {
          // Navigate to the permissions screen if permissions are missing
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => PermissionScreen(
                onAllGranted: () {
                  // After permissions are granted, check if master fingerprint is set up
                  if (!hasMasterFingerprint) {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) =>
                              const MasterFingerprintScreen()),
                    );
                  } else {
                    // If master fingerprint is already set up, go to home screen
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const HomeScreen()),
                    );
                  }
                },
              ),
            ),
          );
        } else if (!hasMasterFingerprint) {
          // If all permissions are granted but no master fingerprint, go to master fingerprint screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
                builder: (context) => const MasterFingerprintScreen()),
          );
        } else {
          // If all conditions are met, go to home screen
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
        }
      }
    } catch (e) {
      print('Exception during initial setup: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Setup Error: ${e.toString()}'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 20),
                  Text(
                    'Setting up your device...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            )
          : const Center(
              child: Text(
                'Setup Complete',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
    );
  }
}
