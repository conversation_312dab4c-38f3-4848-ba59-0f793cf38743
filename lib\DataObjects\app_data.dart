import 'package:flutter/services.dart';
//import 'package:installed_apps/app_info.dart';
//import 'package:installed_apps/installed_apps.dart';

import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Services/category_config.dart';
import 'package:v18ui/Services/category_settings.dart';

class AppData {
  String app_label;
  String package_name;
  bool is_blocked;
  String? blocking_start_time;
  String? blocking_end_time;
  Uint8List icon;
  CategorySettings? categorySettings;

  AppData(
      {required this.app_label,
      this.is_blocked = false,
      required this.package_name,
      this.blocking_start_time,
      this.blocking_end_time,
      required this.icon});

  factory AppData.fromMap(Map<String, dynamic> map) {
    return AppData(
      app_label: map['app_label'],
      package_name: map['package_name'],
      icon: Uint8List.fromList(List<int>.from(map['icon'])),
    );
  }

  static List<AppData> all_apps = [];
  static List<String> all_packages = [];

  static const platform = MethodChannel('AppHandler/AppData');

  static Future<Map<String, dynamic>> checkAppCount() async {
    final totalCount = await platform.invokeMethod('getAppCount');

    // new apps are installed
    if (ConfigManager().config!.isFirstRun) {
      // Set default category settings for all the apps
      CategoryConfig().defaultCategorySettings();
      return {"response": "Configuring all apps", "check_status": "false"};
    }
    if (ConfigManager().config!.configuredAppCount < totalCount) {
      return {"response": "New app install detected", "check_status": "false"};
      // add a mechanism to detect the new app install by comparing with packages in category_settings and then configure it
    }
    if (ConfigManager().config!.configuredAppCount > totalCount) {
      /** We need to remove that app from CategoriseSettings and blocking config*/
      return {
        "response":
            "Looks like you have recently deleted any app ! Let me reset configurations",
        "check_status": "false"
      };
    } else
      return {"check_status": "true"};
  }

  /** This function is created to be used in category_config.dart because during first run 
   * we need to load all the packages and then categorize them.
   */

  static Future<void> load_all_packages() async {
    final List<dynamic> result = await platform.invokeMethod('AppList');
    List<String> app_info =
        List<String>.from(result.map((e) => e['package_name']).toList());
    all_packages = app_info;
  }

  static Future<void> load_all_apps() async {
    List<AppData> blocked_apps = await get_blocked_app_info();
    //List<AppInfo> app_info = await InstalledApps.getInstalledApps(true, true);
    final List<dynamic> result = await platform.invokeMethod('AppList');
    List<AppData> app_info = result
        .map((e) => AppData.fromMap(Map<String, dynamic>.from(e)))
        .toList();
    app_info.sort((a, b) => a.app_label.compareTo(b.app_label));

    /** List of system apps is already defined in AppData.java */
    //List<String> system_apps = await included_system_packages();
    blocked_apps.map((e) {
      app_info.firstWhere((element) => element.package_name == e.package_name)
        ..is_blocked = true
        ..blocking_start_time = e.blocking_start_time
        ..blocking_end_time = e.blocking_end_time;
    }).toList();
    all_apps = app_info;
  }

  /** For various blocking performed by various services like porn deaddiction , gambling etc we need to show 
   * respective app info , hence created this function , implementation for this is not complete. Take app info
   * from method channel
   */
  static getAppInfo(String package_name) {
    MethodChannel platform = MethodChannel('AppHandler/AppData');
  }

  static Future<List<AppData>> get_blocked_app_info() async {
    MethodChannel platform = MethodChannel('AppHandler/AppData');

    List<dynamic> blocked_app_info;
    List<AppData> blocked_apps = [];
    try {
      var services = ConfigManager().services;
      blocked_app_info = services!.appBlockService?.appsBlocked ?? [];

      for (var app_data in blocked_app_info) {
        Uint8List icon = Uint8List.fromList(List<int>.from(await platform
            .invokeMethod('getIcon', {'package_name': app_data.packageName})));
        blocked_apps.add(AppData(
          app_label: app_data.appLabel,
          package_name: app_data.packageName,
          blocking_start_time: app_data.blockingStartTime,
          blocking_end_time: app_data.blockingEndTime,
          is_blocked: true,
          icon: icon,
        ));
      }
      print(blocked_apps);
    } catch (e) {
      print("Config read error : $e");
    }
    return blocked_apps;
  }

  static Future<List<String>> getInstalledBrowsers() async {
    try {
      final List<dynamic> result =
          await platform.invokeMethod('getInstalledBrowsers');
      return result.cast<String>();
    } catch (e) {
      print('Failed to get browsers: $e');
      return [];
    }
  }

  /** As of now we are keeping hardcoded values of only two system apps , 
   *  chrome and youtube , so we are not using this method. becasue we are not 
   *  showing any system apps in the app list screen for the user to block , so there
   * wont be any request ever from user to block system apps. so this function is 
   * not required as of now.
   * 
   */

  /*static Future<List<String>> included_system_packages() async {
    var json_data = await rootBundle.loadString('assets/config.json');
    List<String> system_apps =
        jsonDecode(json_data)['system_apps'].cast<String>().toList();
    return system_apps;
  }*/
}
