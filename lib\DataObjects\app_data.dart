import 'package:flutter/services.dart';
//import 'package:installed_apps/app_info.dart';
//import 'package:installed_apps/installed_apps.dart';

import 'package:v18ui/DataObjects/config_manager.dart';

class AppData {
  String app_label;
  String package_name;
  bool is_blocked;
  String? blocking_start_time;
  String? blocking_end_time;
  Uint8List? icon;

  AppData(
      {required this.app_label,
      required this.is_blocked,
      required this.package_name,
      this.blocking_start_time,
      this.blocking_end_time,
      this.icon});

  static List<AppData> all_apps = [];

  factory AppData.fromMap(Map<String, dynamic> map) {
    return AppData(
      app_label: map['app_label'],
      package_name: map['package_name'],
      is_blocked: false,
      icon: map['icon'],
    );
  }

  static const platform = MethodChannel('AppHandler/AppData');

  static Future<void> load_all_apps(
      {required Function(double) onProgress}) async {
    List<AppData> blocked_apps = await get_blocked_app_info();
    //List<AppInfo> app_info = await InstalledApps.getInstalledApps(true, true);
    final List<dynamic> result = await platform.invokeMethod('AppList');
    List<AppData> app_info = result
        .map((e) => AppData.fromMap(Map<String, dynamic>.from(e)))
        .toList();
    app_info.sort((a, b) => a.app_label.compareTo(b.app_label));

    /** List of system apps is already defined in AppData.java */
    //List<String> system_apps = await included_system_packages();

    try {
      // system apps are received from method channel data itself
      /*if (system_apps.isNotEmpty) {
        for (var package_names in system_apps) {
          var system_app_info = await InstalledApps.getAppInfo(package_names);
          app_info.add(system_app_info!);
        }
      }*/

      int totalApps = app_info.length;

      for (int i = 0; i < totalApps; i++) {
        var app = app_info[i];
        if (blocked_apps
            .any((element) => element.package_name == app.package_name)) {
          app.blocking_start_time = blocked_apps
              .firstWhere((element) => element.package_name == app.package_name)
              .blocking_start_time;
          app.blocking_end_time = blocked_apps
              .firstWhere((element) => element.package_name == app.package_name)
              .blocking_end_time;
          app.is_blocked = true;
        }

        // Update progress
        double progress = (i + 1) / totalApps;
        onProgress(progress);
      }
      all_apps = app_info;
    } catch (e) {
      print("Error in loading apps : $e");
    }
  }

  static Future<List<AppData>> get_blocked_app_info() async {
    MethodChannel platform = MethodChannel('AppHandler/AppData');

    List<dynamic> blocked_app_info;
    List<AppData> blocked_apps = [];
    try {
      var services = ConfigManager().services;
      blocked_app_info = services!.appBlockService?.appsBlocked ?? [];

      for (var app_data in blocked_app_info) {
        Uint8List icon = Uint8List.fromList(List<int>.from(await platform
            .invokeMethod('getIcon', {'package_name': app_data.packageName})));
        blocked_apps.add(AppData(
          app_label: app_data.appLabel,
          package_name: app_data.packageName,
          blocking_start_time: app_data.blockingStartTime,
          blocking_end_time: app_data.blockingEndTime,
          is_blocked: true,
          icon: icon,
        ));
      }
      print(blocked_apps);
    } catch (e) {
      print("Config read error : $e");
    }
    return blocked_apps;
  }

  /** As of now we are keeping hardcoded values of only two system apps , 
   *  chrome and youtube , so we are not using this method. becasue we are not 
   *  showing any system apps in the app list screen for the user to block , so there
   * wont be any request ever from user to block system apps. so this function is 
   * not required as of now.
   * 
   */

  /*static Future<List<String>> included_system_packages() async {
    var json_data = await rootBundle.loadString('assets/config.json');
    List<String> system_apps =
        jsonDecode(json_data)['system_apps'].cast<String>().toList();
    return system_apps;
  }*/
}
