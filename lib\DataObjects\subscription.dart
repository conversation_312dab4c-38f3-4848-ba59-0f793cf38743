class Subscription {
  final String status;
  final String expiryDate;

  Subscription({
    required this.status,
    required this.expiryDate,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      status: json['status'],
      expiryDate: json['expiry_date'],
    );
  }
  Map<String, dynamic> toJson() => {
        'status': status,
        'expiry_date': expiryDate,
      };
}
