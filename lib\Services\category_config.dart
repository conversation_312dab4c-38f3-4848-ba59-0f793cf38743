import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:v18ui/DataObjects/app_data.dart';
import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/Services/category_settings.dart';
import 'package:v18ui/Services/gpt_service.dart';

class CategoryConfig {
  /** This function is called only once during first run of the app */
  Future<Map<String, CategorySettings>> defaultCategorySettings() async {
    final String commonCategorisedAppsString =
        await rootBundle.loadString(FilePaths.common_categorised_apps);
    await AppData.load_all_packages();
    Map<String, dynamic> decodedMap =
        (jsonDecode(commonCategorisedAppsString))['SystemCategorised'];
    Map<String, List<String>> commonCategorydata = decodedMap.map(
      (key, value) => MapEntry(key, List<String>.from(value)),
    );
    dynamic defaultCategorySettings = await _categorizeApps(
        AppData.all_packages, Map.from(commonCategorydata));
    saveCategoryConfig(FilePaths.categorySettings, defaultCategorySettings);
    return defaultCategorySettings;
  }

  Future<Map<String, CategorySettings>> _categorizeApps(
    List<String> installedPackages,
    Map<String, List<String>> predefinedCategories,
  ) async {
    final Map<String, CategorySettings> result = {};
    final List<String> uncategorized = [];

    final Map<String, String> packageToCategory = {};
    // Filter web browsers
    List<String> installedBrowsers = await AppData.getInstalledBrowsers();

    for (var pkg in installedBrowsers) {
      packageToCategory[pkg] = "Browsers";
    }

    // Step 1: Build reverse lookup: package → category name

    for (var entry in predefinedCategories.entries) {
      for (var pkg in entry.value) {
        packageToCategory[pkg] = entry.key;
      }
    }

    // Step 2: Categorize in a single pass
    for (var pkg in installedPackages) {
      final category = packageToCategory[pkg];
      if (category != null) {
        result.putIfAbsent(
            category,
            () => CategorySettings(
                  dailyAllowedTime: "",
                  package: [],
                  timeoutOver: false,
                  todayTotalWatchTime: "",
                  categoryName: category,
                  categoryType: "System_generated",
                  weeklyOffAllowed: false,
                ));
        result[category]!.package.add(pkg);
      } else {
        uncategorized.add(pkg);
      }
    }

    // Step 3: Ask GPT to classify uncategorized packages

    // Following code is working absolutely fine , but it is commented as of now
    // to save GPT credits usage.

    /*if (uncategorized.isNotEmpty) {
      final gptResults = await findCategory(uncategorized);

      for (var entry in gptResults.entries) {
        result.putIfAbsent(
            entry.key,
            () => CategorySettings(
                  dailyAllowedTime: "",
                  package: [],
                  timeoutOver: false,
                  todayTotalWatchTime: "",
                  categoryName: entry.key,
                  categoryType: "System_generated",
                  weeklyOffAllowed: false,
                ));
        result[entry.key]!.package.addAll(entry.value);
      }
    }*/

    return result;
  }

  Future<Map<String, List<String>>> findCategory(
      List<String> unCategorisedApps) async {
    final gpt = GPTService(
        // remove it from here
        apiKey:
            "********************************************************************************************************************************************************************");

    final result = await gpt.categorizePackages(unCategorisedApps);

    print(result);
    return result;
  }

  addCategory(String packageName) {}

  deleteCategory(String packageName) {}

  saveCategoryConfig(
      String filepath, Map<String, CategorySettings> categorySettings) async {
    String jsonString = json.encode(categorySettings);
    File categorySettingsFile = File(filepath);
    await categorySettingsFile.writeAsString(jsonString);
    print("✅ Category config file updated!");
  }
}
