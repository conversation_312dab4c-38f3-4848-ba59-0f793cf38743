import 'dart:convert';
import 'dart:io';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/Services/block_apps_service.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';
import 'package:v18ui/Services/quit_gambling_service.dart';
import 'package:v18ui/Services/safe_browsing.dart';

class Service {
  BlockAppsService? appBlockService;
  PornDeaddictionService?
      pornDeaddictionService; // This is brahmacharya service
  List<String>? safeAppList;
  SafeBrowsingService? safeBrowsing;
  QuitGamblingService? quitGamblingService;

  Service(
      {this.appBlockService,
      this.pornDeaddictionService,
      this.safeAppList,
      this.safeBrowsing,
      this.quitGamblingService});

  factory Service.fromJson(Map<String, dynamic> servicesData) {
    return Service(
      appBlockService:
          BlockAppsService.fromJson(servicesData['appBlockService']),
      pornDeaddictionService: PornDeaddictionService.fromJson(
          servicesData['pornDeaddictionService']),
      safeAppList: List<String>.from(servicesData['safeAppList']),
      safeBrowsing: SafeBrowsingService.fromJson(servicesData['safeBrowsing']),
      quitGamblingService:
          QuitGamblingService.fromJson(servicesData['quitGamblingService']),
    );
  }

  Map<String, dynamic> toJson() => {
        'appBlockService': appBlockService?.toJson(),
        'pornDeaddictionService': pornDeaddictionService?.toJson(),
        'safeAppList': safeAppList ?? [],
        'safeBrowsing': safeBrowsing?.toJson(),
        'quitGamblingService': quitGamblingService?.toJson(),
      };

  Future<bool> updateService(String key, dynamic value) async {
    try {
      String jsonString = await ConfigManager().servicesFile.readAsString();
      Map<String, dynamic> serviceJson = json.decode(jsonString);

      serviceJson[key] = value;

      // Write back to file
      await ConfigManager()
          .servicesFile
          .writeAsString(json.encode(serviceJson));
      return true;
    } catch (e) {
      print('Error updating service: $e');
      return false;
    }
  }

  // Only custom blocking created by the user is allowed to be deleted , services which are created by the app cannot get deleted
  Future<bool> deleteService(String key) async {
    try {
      String jsonString = await ConfigManager().servicesFile.readAsString();
      Map<String, dynamic> serviceJson = json.decode(jsonString);

      // Remove the service if it exists
      if (serviceJson['Services'] != null) {
        serviceJson['Services'].remove(key);

        // Write back to file
        await ConfigManager()
            .servicesFile
            .writeAsString(json.encode(serviceJson));
      }
      return true;
    } catch (e) {
      print('Error deleting service: $e');
      return false;
    }
  }

  // Create Default Services (on First Run) (empty service json)
  static Future<Service> createDefaultServices() async {
    Service _service = Service(
      appBlockService: BlockAppsService(serviceActive: false),
      pornDeaddictionService:
          PornDeaddictionService(serviceActive: false, CalendarData: {}),
      safeAppList: [],
      safeBrowsing: SafeBrowsingService(
          blockingDomainVersion: 0, lastDomainSyncDate: DateTime.now()),
      quitGamblingService: QuitGamblingService(serviceActive: false),
    );
    return _service;
  }

  // Save Modified Services Back to Local Storage
  static Future<void> saveServices(services) async {
    if (services != null) {
      String jsonString = json.encode(services?.toJson());
      File servicesFile = File(FilePaths.services);
      await servicesFile.writeAsString(jsonString);
      print("✅ Services file updated!");
    }
  }
}
