import 'package:flutter/material.dart';
import '../permissions/permission_manager.dart';
import '../permissions/permission_status.dart';
import '../permissions/permission_types.dart';
import '../widgets/permission_card.dart';

/// Screen for requesting permissions from the user.
/// This screen shows a welcome message and then guides the user through
/// granting the necessary permissions.
class PermissionScreen extends StatefulWidget {
  /// Callback that is called when all permissions are granted.
  final VoidCallback? onAllGranted;

  /// Creates a new permission screen.
  const PermissionScreen({super.key, this.onAllGranted});

  @override
  State<PermissionScreen> createState() => _PermissionScreenState();
}

class _PermissionScreenState extends State<PermissionScreen>
    with WidgetsBindingObserver {
  late PageController _pageController;
  List<Widget> _pages = [];
  int _currentPageIndex = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _pageController = PageController();
    _loadPermissions();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _pageController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.resumed) {
      // When the app resumes, check if we're on the Device Admin permission page
      if (_currentPageIndex > 0 && _currentPageIndex < _pages.length - 1) {
        Widget currentPage = _pages[_currentPageIndex];
        if (currentPage is PermissionCard &&
            currentPage.permission.type == PermissionType.deviceAdmin) {
          // Check if the Device Admin permission has been granted
          final bool isGranted = await PermissionManager.isPermissionGranted(
              PermissionType.deviceAdmin);

          if (isGranted) {
            // If granted, move to the next page
            _goToNextPage();
          }
        } else {
          // For other permissions, use the standard check
          _checkCurrentPermissionStatus();
        }
      } else {
        // For other pages, use the standard check
        _checkCurrentPermissionStatus();
      }
    }
  }

  /// Loads the permissions that need to be requested.
  Future<void> _loadPermissions() async {
    final List<PermissionStatus> missingPermissions =
        await PermissionManager.getMissingPermissions();

    setState(() {
      _pages = [
        ...missingPermissions
            .map((permission) => _buildPermissionPage(permission)),
        _buildCompletionPage(),
      ];
      _isLoading = false;
    });

    // If there are no missing permissions, go directly to the completion page
    if (missingPermissions.isEmpty) {
      _goToPage(_pages.length - 1);
    } else {
      // Auto transition from intro page after a delay
      Future.delayed(const Duration(seconds: 2), () {
        if (_currentPageIndex == 0 && mounted) {
          _goToNextPage();
        }
      });
    }
  }

  /// Checks if the current permission has been granted.
  /// If it has, moves to the next page.
  Future<void> _checkCurrentPermissionStatus() async {
    // Skip if we're on the intro or completion page
    if (_currentPageIndex == 0 ||
        _currentPageIndex == _pages.length - 1 ||
        _pages.isEmpty) {
      return;
    }

    // Get the permission from the current page
    Widget currentPage = _pages[_currentPageIndex];
    if (currentPage is PermissionCard) {
      PermissionStatus permission = currentPage.permission;

      // Check if this specific permission is granted
      bool isGranted =
          await PermissionManager.isPermissionGranted(permission.type);

      if (isGranted) {
        // Current permission is granted, check if there are more permissions to grant
        final List<PermissionStatus> remainingMissingPermissions =
            await PermissionManager.getMissingPermissions();

        if (remainingMissingPermissions.isEmpty) {
          // All permissions granted, go to completion page
          _goToPage(_pages.length - 1);
        } else {
          // Move to the next permission page
          _goToNextPage();
        }
      }
    }
  }

  /// Goes to the next page.
  void _goToNextPage() {
    if (_currentPageIndex < _pages.length - 1) {
      _goToPage(_currentPageIndex + 1);
    }
  }

  /// Goes to a specific page.
  void _goToPage(int pageIndex) {
    setState(() {
      _currentPageIndex = pageIndex;
    });

    _pageController.animateToPage(
      pageIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    // If we're on the completion page, call the onAllGranted callback
    if (pageIndex == _pages.length - 1 && widget.onAllGranted != null) {
      widget.onAllGranted!();
    }
  }

  /// Handles the request for a permission.
  /// Opens the settings screen for the permission.
  Future<void> _requestPermission(PermissionStatus permission) async {
    // For Device Admin, we need special handling because it requires user interaction
    if (permission.type == PermissionType.deviceAdmin) {
      // Just open the settings and return - don't set up any automatic checks
      // The user will need to manually grant the permission and return to the app
      await PermissionManager.openPermissionSettings(permission.type);

      // For Device Admin, we need to periodically check if the permission has been granted
      // This is done in the didChangeAppLifecycleState method when the app is resumed
      return;
    }

    // For other permissions, just open settings and let the app lifecycle handle the rest
    await PermissionManager.openPermissionSettings(permission.type);

    // The app lifecycle method (didChangeAppLifecycleState) will automatically
    // check the permission status when the user returns from settings
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(), // Disable swiping
        onPageChanged: (index) {
          setState(() {
            _currentPageIndex = index;
          });
        },
        children: _pages,
      ),
    );
  }

  /// Builds the intro page.
  /*Widget _buildIntroPage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/icons/v18_logo.png', height: 120),
            const SizedBox(height: 32),
            const Text(
              'Welcome to V18!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'We need a few permissions to help you maintain digital wellbeing and control your app usage.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: _goToNextPage,
              child: const Text('Get Started'),
            ),
          ],
        ),
      ),
    );
  }*/

  /// Builds a page for requesting a specific permission.
  Widget _buildPermissionPage(PermissionStatus permission) {
    return PermissionCard(
      permission: permission,
      onRequestPermission: () => _requestPermission(permission),
    );
  }

  /// Builds the completion page.
  Widget _buildCompletionPage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/icons/all_set_sticker.png', height: 120),
            const SizedBox(height: 32),
            const Text(
              'All Set!',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'You\'ve granted all the necessary permissions. You\'re ready to set up your master fingerprint!',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                // Use Future.microtask to ensure the callback is called after the current frame
                // This prevents the gesture handler issue
                if (widget.onAllGranted != null) {
                  Future.microtask(() {
                    if (mounted) {
                      widget.onAllGranted!();
                    }
                  });
                }
              },
              child: const Text('Continue'),
            ),
          ],
        ),
      ),
    );
  }
}
