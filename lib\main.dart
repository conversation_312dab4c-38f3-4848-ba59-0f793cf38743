import 'package:flutter/material.dart';
import 'package:v18ui/Screens/splash_screen.dart';

void main() async {
  // Ensures Flutter is initialized before loading config
  WidgetsFlutterBinding.ensureInitialized();

  runApp(const AppHandler());
}

class AppHandler extends StatelessWidget {
  const AppHandler({super.key});

  static final RouteObserver<ModalRoute<void>> routeObserver =
      RouteObserver<ModalRoute<void>>();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(primarySwatch: Colors.teal),
      home: const SplashScreen(),
      navigatorObservers: [routeObserver],
    );
  }
}
