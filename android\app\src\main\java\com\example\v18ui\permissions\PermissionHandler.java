package com.example.v18ui.permissions;

/**
 * Interface for permission handlers.
 * All permission handlers must implement this interface.
 */
public interface PermissionHandler {
    /**
     * Checks if the permission is granted.
     * @return true if the permission is granted, false otherwise
     */
    boolean isPermissionGranted();
    
    /**
     * Opens the settings screen for the permission.
     */
    void openSettings();
}
