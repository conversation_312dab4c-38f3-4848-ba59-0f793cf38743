package com.example.v18ui.permissions;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.example.v18ui.services.AppAccessibilityService;

/**
 * Handles accessibility permission operations.
 * This class provides methods to check if the accessibility permission is granted
 * and to open the accessibility settings.
 */
public class AccessibilityPermission implements PermissionHandler {
    private static final String TAG = "AccessibilityPermission";
    private final Context context;
    
    public AccessibilityPermission(Context context) {
        this.context = context;
    }
    
    @Override
    public boolean isPermissionGranted() {
        // First try a direct check if our service is running
        if (isAccessibilityServiceRunning()) {
            Log.d(TAG, "Accessibility service is running (direct check)");
            return true;
        }
        
        // Then check if the service is enabled in accessibility settings
        int accessibilityEnabled = 0;
        final String serviceName = context.getPackageName() + "/com.example.v18ui.services.AppAccessibilityService";
        
        try {
            accessibilityEnabled = Settings.Secure.getInt(
                    context.getContentResolver(),
                    Settings.Secure.ACCESSIBILITY_ENABLED
            );
        } catch (Settings.SettingNotFoundException e) {
            Log.e(TAG, "Accessibility setting not found", e);
            return false;
        }
        
        if (accessibilityEnabled == 1) {
            String settingValue = Settings.Secure.getString(
                    context.getContentResolver(),
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            );
            
            if (settingValue != null) {
                Log.d(TAG, "Enabled accessibility services: " + settingValue);
                String[] enabledServices = TextUtils.split(settingValue, ":");
                
                for (String componentName : enabledServices) {
                    if (componentName.equalsIgnoreCase(serviceName)) {
                        Log.d(TAG, "Accessibility service found and enabled!");
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    @Override
    public void openSettings() {
        try {
            Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Failed to open Accessibility Settings", e);
        }
    }
    
    /**
     * Checks if the accessibility service is currently running.
     * @return true if the service is running, false otherwise
     */
    private boolean isAccessibilityServiceRunning() {
        try {
            ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
                if (AppAccessibilityService.class.getName().equals(service.service.getClassName())) {
                    return true;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking if accessibility service is running", e);
        }
        return false;
    }
}
