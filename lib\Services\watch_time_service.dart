import 'dart:async';
import 'package:flutter/material.dart';

class WatchTimeService {
  final Duration dailyLimit;
  final ValueNotifier<String> timeDisplay = ValueNotifier("30:00");

  Duration _totalWatchTime = Duration.zero;
  bool _isBlocked = false;

  final Stopwatch _stopwatch = Stopwatch();
  Timer? _uiTimer;

  WatchTimeService({this.dailyLimit = const Duration(minutes: 30)});

  void start() {
    if (_isBlocked) return;
    if (!_stopwatch.isRunning) {
      _stopwatch.start();
      _startUITimer();
    }
  }

  void pause() {
    if (_stopwatch.isRunning) {
      _stopwatch.stop();
      _totalWatchTime += _stopwatch.elapsed;
      _stopwatch.reset();
      _uiTimer?.cancel();
      _updateTimeDisplay();

      if (_totalWatchTime >= dailyLimit) {
        _isBlocked = true;
      }
    }
  }

  void _startUITimer() {
    _uiTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateTimeDisplay();

      if ((_totalWatchTime + _stopwatch.elapsed) >= dailyLimit) {
        pause();
      }
    });
  }

  void _updateTimeDisplay() {
    final remaining = dailyLimit - _totalWatchTime - _stopwatch.elapsed;
    final m = remaining.inMinutes.remainder(60).toString().padLeft(2, '0');
    final s = remaining.inSeconds.remainder(60).toString().padLeft(2, '0');
    timeDisplay.value = remaining.isNegative ? "00:00" : "$m:$s";
  }

  bool get isBlocked => _isBlocked;

  void reset() {
    _totalWatchTime = Duration.zero;
    _stopwatch.reset();
    _stopwatch.stop();
    _uiTimer?.cancel();
    _isBlocked = false;
    timeDisplay.value = "${dailyLimit.inMinutes.toString().padLeft(2, '0')}:00";
  }
}
