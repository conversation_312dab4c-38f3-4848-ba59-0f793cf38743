import 'package:v18ui/DataObjects/subscription.dart';

// Config class with device ID support
class Config {
  final String userId;
  bool isFirstRun;
  final Subscription subscription;
  final bool isMasterFingerprintActivated;

  Config({
    required this.userId,
    required this.isFirstRun,
    required this.subscription,
    required this.isMasterFingerprintActivated,
  });

  factory Config.fromJson(Map<String, dynamic> json) {
    return Config(
      userId: json['userId'],
      isFirstRun: json['isFirstrun'],
      subscription: Subscription.fromJson(json['subscription']),
      isMasterFingerprintActivated: json['isMasterFingerprintActivated'],
    );
  }

  Map<String, dynamic> toJson() => {
        'userId': userId,
        'isFirstrun': isFirstRun,
        'subscription': subscription.toJson(),
        'isMasterFingerprintActivated': isMasterFingerprintActivated,
      };
}
