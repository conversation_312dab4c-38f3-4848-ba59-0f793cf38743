import 'dart:convert';
import 'dart:io';

import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/DataObjects/subscription.dart';
import 'package:v18ui/Utils/device_utils.dart';

// Config class with device ID support
class Config {
  final String userId;
  bool isFirstRun;
  final Subscription subscription;
  final bool isMasterFingerprintActivated;
  final int configuredAppCount;

  Config(
      {required this.userId,
      required this.isFirstRun,
      required this.subscription,
      required this.isMasterFingerprintActivated,
      required this.configuredAppCount});

  factory Config.fromJson(Map<String, dynamic> json) {
    return Config(
      userId: json['userId'],
      isFirstRun: json['isFirstrun'],
      subscription: Subscription.fromJson(json['subscription']),
      isMasterFingerprintActivated: json['isMasterFingerprintActivated'],
      configuredAppCount: json['configuredAppCount'],
    );
  }

  Map<String, dynamic> toJson() => {
        'userId': userId,
        'isFirstrun': isFirstRun,
        'subscription': subscription.toJson(),
        'isMasterFingerprintActivated': isMasterFingerprintActivated,
        'configuredAppCount': configuredAppCount
      };

  // ✅ Create Default Config (on First Run)
  static Future<Config> createDefaultConfig() async {
    // Get device ID
    String deviceId = await DeviceUtils.getDeviceId();

    var config = Config(
        userId: deviceId, // use device_id as user_id
        isFirstRun: true,
        subscription: Subscription(
          status: "inactive",
          expiryDate: DateTime.now().toIso8601String(),
        ),
        isMasterFingerprintActivated: false,
        configuredAppCount: 0);

    await saveConfig(config);

    return config;
  }

  // ✅ Save Modified Config Back to Local Storage
  static Future<void> saveConfig(Config config) async {
    File configFile = File(FilePaths.config);
    String jsonString = json.encode(config.toJson());
    await configFile.writeAsString(jsonString);
    print("✅ Config file updated!");
  }
}
