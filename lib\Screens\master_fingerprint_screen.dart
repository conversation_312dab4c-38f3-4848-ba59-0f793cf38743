import 'package:flutter/material.dart';
import 'package:v18ui/Utils/bio_metric_authenticator.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/config.dart';
import 'package:v18ui/Screens/home_screen.dart';

class MasterFingerprintScreen extends StatefulWidget {
  const MasterFingerprintScreen({Key? key}) : super(key: key);

  @override
  _MasterFingerprintScreenState createState() =>
      _MasterFingerprintScreenState();
}

class _MasterFingerprintScreenState extends State<MasterFingerprintScreen> {
  bool _isRegistering = false;
  bool _isSuccess = false;
  String _statusMessage = "Ready to register your master fingerprint";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Set Up Master Fingerprint"),
        backgroundColor: Colors.redAccent,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              Container(
                width: 150,
                height: 150,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.redAccent,
                ),
                child: const Icon(
                  Icons.fingerprint,
                  size: 100,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 30),
              const Text(
                "Master Fingerprint Setup",
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              const Text(
                "Your master fingerprint will be used to secure the app and prevent unauthorized access.",
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              const Text(
                "This fingerprint cannot be changed later, so please use a fingerprint that you'll remember.",
                style: TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),
              Text(
                _statusMessage,
                style: TextStyle(
                  fontSize: 16,
                  color: _isSuccess ? Colors.green : Colors.black,
                  fontWeight: _isSuccess ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              ElevatedButton(
                onPressed: _isRegistering || _isSuccess
                    ? null
                    : () => _registerMasterFingerprint(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.redAccent,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                  textStyle: const TextStyle(fontSize: 18),
                ),
                child: _isRegistering
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text("Register Fingerprint"),
              ),
              const SizedBox(height: 20),
              if (_isSuccess)
                ElevatedButton(
                  onPressed: () => _proceedToHome(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 40, vertical: 15),
                    textStyle: const TextStyle(fontSize: 18),
                  ),
                  child: const Text("Continue"),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _registerMasterFingerprint() async {
    setState(() {
      _isRegistering = true;
      _statusMessage = "Registering fingerprint...";
    });

    try {
      await BiometricAuth().generateMasterFingerprintKey();

      // Update config to mark master fingerprint as activated
      final configManager = ConfigManager();
      if (configManager.config != null) {
        final updatedConfig = Config(
          userId: configManager.config!.userId,
          isFirstRun: false, // Also set isFirstRun to false
          subscription: configManager.config!.subscription,
          isMasterFingerprintActivated: true,
        );

        // Update the config
        configManager.config = updatedConfig;
        await configManager.saveConfig();
      }

      setState(() {
        _isRegistering = false;
        _isSuccess = true;
        _statusMessage = "Master fingerprint registered successfully!";
      });
    } catch (e) {
      setState(() {
        _isRegistering = false;
        _statusMessage = "Failed to register fingerprint: ${e.toString()}";
      });
    }
  }

  void _proceedToHome() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => HomeScreen()),
    );
  }
}
