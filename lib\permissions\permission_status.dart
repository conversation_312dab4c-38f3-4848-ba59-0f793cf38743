import 'permission_types.dart';

/// Class representing the status of a permission.
/// This class contains information about a permission, such as its type,
/// title, description, and icon path.
class PermissionStatus {
  /// The type of the permission.
  final PermissionType type;
  
  /// The title of the permission.
  final String title;
  
  /// The description of the permission.
  final String description;
  
  /// The path to the icon for the permission.
  final String iconPath;
  
  /// Creates a new permission status.
  PermissionStatus({
    required this.type,
    required this.title,
    required this.description,
    required this.iconPath,
  });
  
  /// Converts the permission status to a map.
  /// This is useful for passing the permission status to the UI.
  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'title': title,
      'description': description,
      'iconPath': iconPath,
    };
  }
}
