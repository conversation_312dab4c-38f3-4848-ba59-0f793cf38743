# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\Download\\flutter_windows_3.7.12-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\self Project\\All versions\\ui\\ui" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\Download\\flutter_windows_3.7.12-stable\\flutter"
  "PROJECT_DIR=C:\\self Project\\All versions\\ui\\ui"
  "FLUTTER_ROOT=D:\\Download\\flutter_windows_3.7.12-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\self Project\\All versions\\ui\\ui\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\self Project\\All versions\\ui\\ui"
  "FLUTTER_TARGET=C:\\self Project\\All versions\\ui\\ui\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9mNDBlOTc2YmVkZmY1N2U2OWUxYjNkODlhN2MyYTNjNjE3YTAzZGFkLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\self Project\\All versions\\ui\\ui\\.dart_tool\\package_config.json"
)
