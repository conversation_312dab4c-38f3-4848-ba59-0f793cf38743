package com.example.v18ui;

import android.app.ActivityManager;
import android.app.AppOpsManager;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.embedding.engine.plugins.FlutterPlugin;

public class Permissions_Settings {

  private static final String TAG = "Permissions_Settings";

  // Open Accessibility Settings
  public static void openAccessibilitySettings(Context context) {
    try {
      Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
      context.startActivity(intent);
    } catch (Exception e) {
      Log.e(TAG, "Failed to open Accessibility Settings", e);
    }
  }

  // Check if Accessibility is Enabled
  public static boolean isAccessibilityEnabled(Context context) {
    // First try a direct check if our service is running
    if (isAccessibilityServiceRunning(context, AppAccessibilityService.class)) {
      Log.d(TAG, "Accessibility service is running (direct check)");
      return true;
    }
    int accessibilityEnabled = 0;

    // Try different possible service name formats
    final String serviceName1 = context.getPackageName() + ".AppAccessibilityService";
    final String serviceName2 = context.getPackageName() + "/com.example.v18ui.AppAccessibilityService";
    final String serviceName3 = "com.example.v18ui/.AppAccessibilityService";
    final String serviceName4 = "com.example.v18ui/com.example.v18ui.AppAccessibilityService";

    // Log the service names we're looking for to help with debugging
    Log.d(TAG, "Checking for accessibility service with multiple possible names:");
    Log.d(TAG, "- " + serviceName1);
    Log.d(TAG, "- " + serviceName2);
    Log.d(TAG, "- " + serviceName3);
    Log.d(TAG, "- " + serviceName4);
    try {
      accessibilityEnabled = Settings.Secure.getInt(
              context.getContentResolver(),
              Settings.Secure.ACCESSIBILITY_ENABLED
      );
    } catch (Settings.SettingNotFoundException e) {
      Log.e(TAG, "Accessibility setting not found", e);
    }

    if (accessibilityEnabled == 1) {
      String settingValue = Settings.Secure.getString(
              context.getContentResolver(),
              Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
      );
      if (settingValue != null) {
        // Log the enabled services string to help with debugging
        Log.d(TAG, "Enabled accessibility services: " + settingValue);

        String[] enabledServices = TextUtils.split(settingValue, ":");
        for (String componentName : enabledServices) {
          Log.d(TAG, "Checking component: " + componentName);

          // Try all possible service name formats
          if (componentName.equalsIgnoreCase(serviceName1) ||
              componentName.equalsIgnoreCase(serviceName2) ||
              componentName.equalsIgnoreCase(serviceName3) ||
              componentName.equalsIgnoreCase(serviceName4)) {
            Log.d(TAG, "Accessibility service found and enabled!");
            return true;
          }
        }

        // If we get here, the service wasn't found in the enabled services
        Log.d(TAG, "Accessibility service not found in enabled services");
      } else {
        Log.d(TAG, "No accessibility services are enabled");
      }
    }
    return false;
  }

  // Open Usage Stats Settings
  public static void openUsageStatsSettings(Context context) {
    try {
      Intent intent = new Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS);
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
      context.startActivity(intent);
    } catch (Exception e) {
      Log.e(TAG, "Failed to open Usage Stats Settings", e);
    }
  }

  // Check if Usage Stats Permission is Granted
  public static boolean isUsageStatsPermissionGranted(Context context) {
    try {
      AppOpsManager appOps = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
      int mode = appOps.checkOpNoThrow(
              "android:get_usage_stats",
              android.os.Process.myUid(),
              context.getPackageName()
      );
      return mode == AppOpsManager.MODE_ALLOWED;
    } catch (Exception e) {
      Log.e(TAG, "Failed to check Usage Stats Permission", e);
      return false;
    }
  }

  // Open Device Admin Settings
  public static void openDeviceAdminSettings(Context context) {
    try {
      // Create the admin component name
      ComponentName deviceAdmin = new ComponentName(context, "com.example.v18ui.receivers.DeviceAdminReceiver");

      // Check if already active
      DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
      if (dpm.isAdminActive(deviceAdmin)) {
        Log.d(TAG, "Device admin is already active");
        return; // Already active, no need to open settings
      }

      // Create intent to add device admin
      Intent intent = new Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN);
      intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, deviceAdmin);
      intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION,
          "Enable device admin to prevent unauthorized uninstallation and improve security.");
      intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

      // Log before starting activity
      Log.d(TAG, "Opening device admin settings with component: " + deviceAdmin.getClassName());
      context.startActivity(intent);
    } catch (Exception e) {
      Log.e(TAG, "Failed to open Device Admin Settings", e);
    }
  }

  // Check if Device Admin is Active
  public static boolean isDeviceAdminActive(Context context) {
    try {
      DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
      ComponentName adminComponent = new ComponentName(context, "com.example.v18ui.receivers.DeviceAdminReceiver");

      boolean isActive = dpm.isAdminActive(adminComponent);
      Log.d(TAG, "Device admin active check: " + isActive +
          " for component: " + adminComponent.getClassName());

      return isActive;
    } catch (Exception e) {
      Log.e(TAG, "Failed to check Device Admin status", e);
      return false;
    }
  }

  // Detach from Flutter Engine
  public void onDetachedFromEngine(@NonNull FlutterPlugin.FlutterPluginBinding binding) {
    // Clean up resources if needed
  }

  // Helper method to check if an accessibility service is running
  private static boolean isAccessibilityServiceRunning(Context context, Class<?> serviceClass) {
    try {
      ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
      for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
        if (serviceClass.getName().equals(service.service.getClassName())) {
          Log.d(TAG, "Found running service: " + service.service.getClassName());
          return true;
        }
      }
      return false;
    } catch (Exception e) {
      Log.e(TAG, "Error checking if service is running", e);
      return false;
    }
  }
}
