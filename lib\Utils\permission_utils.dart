import '../permissions/permission_manager.dart';
import '../permissions/permission_status.dart';
import '../permissions/permission_types.dart';

/// Utility functions for permissions.
class PermissionUtils {
  /// Checks if all required permissions are granted.
  /// Returns true if all permissions are granted, false otherwise.
  static Future<bool> areAllPermissionsGranted() async {
    return await PermissionManager.areAllPermissionsGranted();
  }
  
  /// Gets a list of permissions that are not granted.
  /// Returns a list of PermissionStatus objects.
  static Future<List<PermissionStatus>> getMissingPermissions() async {
    return await PermissionManager.getMissingPermissions();
  }
  
  /// Checks if a specific permission is granted.
  /// Returns true if the permission is granted, false otherwise.
  static Future<bool> isPermissionGranted(PermissionType permission) async {
    return await PermissionManager.isPermissionGranted(permission);
  }
  
  /// Opens the settings screen for a specific permission.
  /// This allows the user to grant the permission.
  static Future<void> openPermissionSettings(PermissionType permission) async {
    await PermissionManager.openPermissionSettings(permission);
  }
  
  /// Gets the name of a permission type as a string.
  /// This is useful for logging and debugging.
  static String getPermissionName(PermissionType permission) {
    switch (permission) {
      case PermissionType.accessibility:
        return 'Accessibility';
      case PermissionType.usageStats:
        return 'Usage Stats';
      case PermissionType.deviceAdmin:
        return 'Device Admin';
    }
  }
  
  /// Gets the description of a permission type as a string.
  /// This is useful for displaying information to the user.
  static String getPermissionDescription(PermissionType permission) {
    switch (permission) {
      case PermissionType.accessibility:
        return 'Required for monitoring app usage.';
      case PermissionType.usageStats:
        return 'Helps us detect app usage time.';
      case PermissionType.deviceAdmin:
        return 'Required for preventing uninstalls.';
    }
  }
}
