import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:v18ui/Services/porn_deaddiction_service.dart';

class BrahmacharyaModeScreen extends StatefulWidget {
  BrahmacharyaModeScreen({super.key});

  @override
  State<BrahmacharyaModeScreen> createState() => _BrahmacharyaModeScreenState();
}

class _BrahmacharyaModeScreenState extends State<BrahmacharyaModeScreen> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  List<DateTime> _allowedDates = [];
  List<DateTime> _watchedDates = [];
  int _allowedCount = 0;
  int _watchedCount = 0;

  _loadMonthData(String month) {
    final data = PornDeaddictionService().getWatchedDates(month);
    setState(() {
      _watchedDates = data.pornWatchedDates;
      _allowedDates = PornDeaddictionService().getStageDates(
          int.parse(month.split('-')[0]),
          int.parse(month.split('-')[1]),
          data.stage);
      _allowedCount = data.allowedCount;
      _watchedCount = data.watchedCount;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadMonthData(PornDeaddictionService.getCurrentMonth());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            "Brahmacharya Mode",
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
            //vertical: MediaQuery.of(context).size.height * 0.02,
            horizontal: MediaQuery.of(context).size.width * 0.05,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCalendar(),
              SizedBox(height: 20),
              _buildLegend(),
              SizedBox(height: 20),
              Text(
                "Overlapping graph:",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Text(
                "Blocked Apps:",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
            ],
          ),
        ));
  }

  Widget _buildCalendar() {
    return TableCalendar(
      firstDay: DateTime.utc(2025, 1, 1),
      lastDay: DateTime.utc(2050, 12, 31),
      focusedDay: _focusedDay,
      selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
      calendarFormat: _calendarFormat,
      onDaySelected: (selectedDay, focusedDay) {
        setState(() {
          _selectedDay = selectedDay;
          _focusedDay = focusedDay;
        });
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Mark Porn Watched Date'),
              content: Text(
                  'Would you like to mark this date as "Porn watched date"?'),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedDay = null;
                    });
                    Navigator.of(context).pop(); // Close dialog
                  },
                  child: Text('Cancel'),
                ),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                  ),
                  onPressed: () {
                    PornDeaddictionService().modifyRecord(
                        focusedDay.year.toString() +
                            '-' +
                            focusedDay.month.toString().padLeft(2, '0'),
                        focusedDay); // Close dialog
                    Navigator.of(context).pop(); // Close dialog
                    // 🔥 Do your operation here, e.g. update watched dates list
                    setState(() {
                      _watchedDates.add(selectedDay);
                      _watchedCount++;
                      _selectedDay = null;
                    });
                    // Optionally show confirmation
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Date marked as watched')),
                    );
                  },
                  child: Text('Add'),
                ),
              ],
            );
          },
        );
      },
      availableCalendarFormats: {
        // 👈 Restrict to only Month format
        CalendarFormat.month: 'Month',
      },
      onPageChanged: (focusedDay) {
        setState(() {
          _focusedDay = focusedDay;
        });
        _loadMonthData(focusedDay.year.toString() +
            '-' +
            focusedDay.month.toString().padLeft(2, '0'));
      },
      calendarBuilders: CalendarBuilders(
        todayBuilder: (context, day, focusedDay) {
          final isAllowed = _allowedDates.contains(day);

          if (isAllowed) {
            return Container(
              margin: EdgeInsets.all(6),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.green, // same as allowed date color
                shape: BoxShape.circle,
              ),
              child: Text(
                '${day.day}',
                style: TextStyle(color: Colors.white),
              ),
            );
          }

          // If today is not allowed, just render it as normal text (no purple circle)
          return Container(
            margin: EdgeInsets.all(6),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.black,
                width: 2,
              ),
              shape: BoxShape.circle,
            ),
            child: Text(
              '${day.day}',
              style: TextStyle(color: Colors.black),
            ),
          );
        },
        defaultBuilder: (context, day, focusedDay) {
          final isWatched =
              _watchedDates.any((watched) => isSameDay(watched, day));
          final isMarked =
              _allowedDates.any((marked) => isSameDay(marked, day));

          Color? fillColor;
          if (isWatched) {
            fillColor = Colors.red;
          } else if (isMarked) {
            fillColor = Colors.green;
          }

          return Container(
            margin: EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: fillColor,
              shape: BoxShape.circle,
            ),
            alignment: Alignment.center,
            child: Text(
              '${day.day}',
              style: TextStyle(
                color: fillColor != null ? Colors.white : Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLegend() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 20,
          children: [
            _legendItem(color: Colors.green, label: "Allowed Dates"),
            _legendItem(color: Colors.red, label: "Watched Dates"),
          ],
        ),
        SizedBox(height: 10),
        Wrap(
          crossAxisAlignment: WrapCrossAlignment.center,
          spacing: 12,
          runSpacing: 8,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 24),
                SizedBox(width: 8),
                Text('Allowed Count: $_allowedCount'),
              ],
            ),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.remove_red_eye, color: Colors.red, size: 24),
                SizedBox(width: 8),
                Text('Watched Count: $_watchedCount'),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _legendItem({required Color color, required String label}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        CircleAvatar(backgroundColor: color, radius: 7),
        SizedBox(width: 6),
        Text(label, style: TextStyle(fontSize: 15)),
      ],
    );
  }

  /// 🔥 Blocked Apps Tile
  Widget blockedAppTile(String appName, Uint8List icon, Color color,
      String start_time, String end_time) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: ListTile(
        leading: Image.memory(
          icon,
          width: 40,
          height: 40,
        ),
        title: Text(
          appName,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        subtitle: Text("Blocked from $start_time till $end_time"),
        trailing: Icon(Icons.lock, color: Colors.red),
        onTap: () {}, // Add custom action if needed
      ),
    );
  }

  /// 🔥 Large Tile with Tap Action
  Widget largeTile(String title, IconData icon, Color color, Function onTap) {
    return GestureDetector(
      onTap: () => onTap(),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: MediaQuery.of(context).size.height * 0.02,
          horizontal: MediaQuery.of(context).size.width * 0.05,
        ),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 32),
                SizedBox(width: 15),
                Text(
                  title,
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 20),
          ],
        ),
      ),
    );
  }
}
