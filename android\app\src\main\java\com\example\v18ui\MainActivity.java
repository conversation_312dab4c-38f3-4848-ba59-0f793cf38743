package com.example.v18ui;

import android.content.pm.ApplicationInfo;

import androidx.annotation.NonNull;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.plugin.common.MethodChannel;

import com.example.v18ui.permissions.PermissionManager;

public class MainActivity extends FlutterFragmentActivity {
    private static final String APP_DATA_CHANNEL = "AppHandler/AppData";

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);

        // Register the app data channel for backward compatibility
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), APP_DATA_CHANNEL)
                .setMethodCallHandler(
                        (call, result) -> {
                            switch(call.method) {
                                case "AppList":
                                    List<Map<String,Object>> app_data = AppData.get_Installed_apps(getApplicationContext());
                                    result.success(app_data);
                                    break;
                                case "getIcon":
                                    byte[] app_icon = AppData.get_icon(getApplicationContext().getPackageManager(),call.argument("package_name"));
                                    result.success(app_icon);
                                    break;
                                // Permission methods
                                case "isAccessibilityServiceEnabled":
                                    result.success(Permissions_Settings.isAccessibilityEnabled(this));
                                    break;
                                case "isUsageStatsEnabled":
                                    result.success(Permissions_Settings.isUsageStatsPermissionGranted(this));
                                    break;
                                case "isDeviceAdminEnabled":
                                    result.success(Permissions_Settings.isDeviceAdminActive(this));
                                    break;
                                case "openAccessibilitySettings":
                                    Permissions_Settings.openAccessibilitySettings(this);
                                    result.success(null);
                                    break;
                                case "openUsageStatsSettings":
                                    Permissions_Settings.openUsageStatsSettings(this);
                                    result.success(null);
                                    break;
                                case "openDeviceAdminSettings":
                                    Permissions_Settings.openDeviceAdminSettings(this);
                                    result.success(null);
                                    break;
                                default:
                                    result.notImplemented();
                                    break;
                            }
                        });

        // Register the permission manager
        // Pass 'this' (the Activity) instead of getApplicationContext() to allow proper activity launching
        PermissionManager.registerWith(flutterEngine, this);
    }
}